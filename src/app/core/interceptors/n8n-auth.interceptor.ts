import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { ConfigService, SupabaseService } from '../services';

export const n8nAuthInterceptor: HttpInterceptorFn = (req, next) => {
  const configService = inject(ConfigService);
  const supabaseService = inject(SupabaseService);

  // Check if the request is going to n8n
  const n8nBaseUrl = configService.n8nBaseUrl;

  if (req.url.startsWith(n8nBaseUrl)) {
    // Get current Supabase session token
    const session = supabaseService.currentSession;
    const token = session?.access_token;

    if (token) {
      // Clone the request and add Authorization header
      const authReq = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });

      return next(authReq);
    }
  }

  // For all other requests, proceed without modification
  return next(req);
};
