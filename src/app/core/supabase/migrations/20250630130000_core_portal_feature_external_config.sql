CREATE TABLE core_portal_feature_external_config (
    portal_feature_id TEXT PRIMARY KEY,
    config J<PERSON>NB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_core_portal_feature_external_config_created_at ON core_portal_feature_external_config(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE core_portal_feature_external_config ENABLE ROW LEVEL SECURITY;

-- Create policies - allow all operations for authenticated users
CREATE POLICY "Enable all operations for authenticated users" ON core_portal_feature_external_config
    FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically set updated_at
CREATE OR REPLACE FUNCTION set_core_portal_feature_external_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    -- Always update updated_at
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for core_portal_feature_external_config
CREATE TRIGGER core_portal_feature_external_config_set_updated_at
    BEFORE INSERT OR UPDATE ON core_portal_feature_external_config
    FOR EACH ROW
    EXECUTE FUNCTION set_core_portal_feature_external_config_updated_at();

-- Add comments for documentation
COMMENT ON TABLE core_portal_feature_external_config IS 'External configuration settings for portal features (stored in database) - Core';
COMMENT ON COLUMN core_portal_feature_external_config.portal_feature_id IS 'Identifier of the portal feature (e.g., ad-winners, fb-accounts-manager)';
COMMENT ON COLUMN core_portal_feature_external_config.config IS 'JSON configuration object for the feature';
