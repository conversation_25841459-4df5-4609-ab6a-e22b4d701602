.app-container {
  min-height: 100vh;
}

.main-content {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  margin-left: 280px; /* Default sidebar width */
}

.main-content.no-sidebar {
  margin-left: 0;
}

/* Mobile Header */
.mobile-header {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  z-index: 997;
  padding: 0 1rem;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: none;
  background: transparent;
  border-radius: 0.75rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.mobile-menu-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 56px;
  height: 56px;
  border-radius: 0.875rem;
  background: transparent;
  transition: all 0.2s ease;
  z-index: -1;
}

.mobile-menu-btn:hover::before {
  background: #f1f5f9;
}

.mobile-menu-btn:hover {
  color: #5521be;
  transform: scale(1.05);
}

.mobile-menu-btn:active {
  transform: scale(0.95);
}

.mobile-logo {
  height: 32px;
  width: auto;
}

/* Main content margin is now handled dynamically via Angular */

/* Mobile Responsive */
@media (max-width: 768px) {
  .mobile-header {
    display: flex !important;
  }

  .main-content {
    margin-left: 0 !important;
    padding-top: 60px;
  }

  .main-content.no-sidebar {
    padding-top: 0;
  }

  /* Hide desktop sidebar toggle on mobile */
  :host ::ng-deep .sidebar .toggle-btn {
    display: none;
  }
}

.content-wrapper {
  padding: 2rem;
  padding-left: 6rem;
  margin: 0 auto;
}

/* Welcome Section */
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 4rem);
}

.welcome-card {
  text-align: center;
  padding: 3rem;
  border-radius: 1rem;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.welcome-card h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  font-family: 'Inter', sans-serif;
}

.welcome-card p {
  font-size: 1.125rem;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.quick-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }

  :host ::ng-deep .sidebar {
    transform: translateX(-100%);
  }

  :host ::ng-deep .sidebar.mobile-open {
    transform: translateX(0);
  }

  .content-wrapper {
    padding: 1rem;
  }

  .welcome-card {
    padding: 2rem;
  }

  .welcome-card h1 {
    font-size: 2rem;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
}
