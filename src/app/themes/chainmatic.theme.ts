import { definePreset } from '@primeng/themes';

const ChainmaticTheme = definePreset({
  semantic: {
    primary: {
      50: '#f3f0ff',
      100: '#e9e2ff',
      200: '#d6c9ff',
      300: '#b8a5ff',
      400: '#9575ff',
      500: '#5521be',
      600: '#4a1ca8',
      700: '#3f1791',
      800: '#35137b',
      900: '#2b0f64',
      950: '#1f0a4a',
    },
    secondary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#e036af',
      600: '#c026d3',
      700: '#a21caf',
      800: '#86198f',
      900: '#701a75',
      950: '#4a044e',
    },
    colorScheme: {
      light: {
        surface: {
          0: 'rgba(255, 255, 255, 0.95)',
          50: '#feecf9',
          100: 'rgba(253, 242, 248, 0.8)',
          200: 'rgba(252, 231, 243, 0.6)',
          300: 'rgba(251, 207, 232, 0.4)',
          400: '#f9a8d4',
          500: '#f472b6',
          600: '#e036af',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
          950: '#500724',
        },
        primary: {
          color: '#5521be',
          contrastColor: '#ffffff',
          hoverColor: '#4a1ca8',
          activeColor: '#3f1791',
        },
        highlight: {
          background: '#5521be',
          focusBackground: '#4a1ca8',
          color: '#ffffff',
          focusColor: '#ffffff',
        },
        mask: {
          background: 'rgba(0,0,0,0.4)',
          color: '#ffffff',
        },
        formField: {
          background: '#ffffff',
          disabledBackground: '#f8fafc',
          filledBackground: '#f1f5f9',
          filledHoverBackground: '#e2e8f0',
          filledFocusBackground: '#ffffff',
          borderColor: '#e2e8f0',
          hoverBorderColor: '#cbd5e1',
          focusBorderColor: '#5521be',
          invalidBorderColor: '#ef4444',
          color: '#1e293b',
          disabledColor: '#64748b',
          placeholderColor: '#94a3b8',
          floatLabelColor: '#64748b',
          floatLabelFocusColor: '#5521be',
          floatLabelActiveColor: '#5521be',
          floatLabelInvalidColor: '#ef4444',
          iconColor: '#64748b',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        text: {
          color: '#1e293b',
          hoverColor: '#0f172a',
          mutedColor: '#64748b',
          hoverMutedColor: '#475569',
        },
        content: {
          background: '#ffffff',
          hoverBackground: '#f8fafc',
          borderColor: '#e2e8f0',
          color: '#475569',
          hoverColor: '#1e293b',
        },
        overlay: {
          select: {
            background: '#ffffff',
            borderColor: '#e2e8f0',
            color: '#1e293b',
          },
          popover: {
            background: '#ffffff',
            borderColor: '#e2e8f0',
            color: '#1e293b',
            shadow:
              '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          },
          modal: {
            background: '#ffffff',
            borderColor: '#e2e8f0',
            color: '#1e293b',
          },
        },
        list: {
          option: {
            focusBackground: '#f1f5f9',
            selectedBackground: '#5521be',
            selectedFocusBackground: '#4a1ca8',
            color: '#1e293b',
            focusColor: '#1e293b',
            selectedColor: '#ffffff',
            selectedFocusColor: '#ffffff',
            icon: {
              color: '#64748b',
              focusColor: '#64748b',
            },
          },
        },
        navigation: {
          item: {
            focusBackground: '#f1f5f9',
            activeBackground: '#5521be',
            color: '#475569',
            focusColor: '#1e293b',
            activeColor: '#ffffff',
            icon: {
              color: '#64748b',
              focusColor: '#64748b',
              activeColor: '#ffffff',
            },
          },
        },
      },
    },
  },
  components: {
    button: {
      root: {
        paddingX: '0.75rem',
        paddingY: '0.375rem',
        borderRadius: '0.375rem',
        gap: '0.375rem',
        fontWeight: '500',
        fontSize: '0.875rem',
        borderWidth: '1px',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 3px rgba(85, 33, 190, 0.12)',
        },
        transitionDuration: '0.15s',
      },
      label: {
        fontWeight: '500',
        fontSize: '0.875rem',
      },
      icon: {
        size: '1rem',
      },
      badge: {
        size: '1rem',
        fontSize: '0.75rem',
      },
      raisedShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      roundedBorderRadius: '0.5rem',
      colorScheme: {
        light: {
          root: {
            primary: {
              background: '#5521be',
              hoverBackground: '#4a1ca8',
              activeBackground: '#3f1791',
              borderColor: '#5521be',
              hoverBorderColor: '#4a1ca8',
              activeBorderColor: '#3f1791',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            secondary: {
              background: 'transparent',
              hoverBackground: '#f1f5f9',
              activeBackground: '#e2e8f0',
              borderColor: '#e2e8f0',
              hoverBorderColor: '#cbd5e1',
              activeBorderColor: '#94a3b8',
              color: '#475569',
              hoverColor: '#1e293b',
              activeColor: '#0f172a',
            },
            info: {
              background: '#0ea5e9',
              hoverBackground: '#0284c7',
              activeBackground: '#0369a1',
              borderColor: '#0ea5e9',
              hoverBorderColor: '#0284c7',
              activeBorderColor: '#0369a1',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            success: {
              background: '#10b981',
              hoverBackground: '#059669',
              activeBackground: '#047857',
              borderColor: '#10b981',
              hoverBorderColor: '#059669',
              activeBorderColor: '#047857',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            warn: {
              background: '#f59e0b',
              hoverBackground: '#d97706',
              activeBackground: '#b45309',
              borderColor: '#f59e0b',
              hoverBorderColor: '#d97706',
              activeBorderColor: '#b45309',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            help: {
              background: '#8b5cf6',
              hoverBackground: '#7c3aed',
              activeBackground: '#6d28d9',
              borderColor: '#8b5cf6',
              hoverBorderColor: '#7c3aed',
              activeBorderColor: '#6d28d9',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            danger: {
              background: '#ef4444',
              hoverBackground: '#dc2626',
              activeBackground: '#b91c1c',
              borderColor: '#ef4444',
              hoverBorderColor: '#dc2626',
              activeBorderColor: '#b91c1c',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
            contrast: {
              background: '#1e293b',
              hoverBackground: '#0f172a',
              activeBackground: '#020617',
              borderColor: '#1e293b',
              hoverBorderColor: '#0f172a',
              activeBorderColor: '#020617',
              color: '#ffffff',
              hoverColor: '#ffffff',
              activeColor: '#ffffff',
            },
          },
          outlined: {
            primary: {
              hoverBackground: 'rgba(85, 33, 190, 0.04)',
              activeBackground: 'rgba(85, 33, 190, 0.08)',
              borderColor: '#5521be',
              color: '#5521be',
            },
            secondary: {
              hoverBackground: 'rgba(71, 85, 105, 0.04)',
              activeBackground: 'rgba(71, 85, 105, 0.08)',
              borderColor: '#475569',
              color: '#475569',
            },
          },
          text: {
            primary: {
              hoverBackground: 'rgba(85, 33, 190, 0.04)',
              activeBackground: 'rgba(85, 33, 190, 0.08)',
              color: '#5521be',
            },
            secondary: {
              hoverBackground: 'rgba(71, 85, 105, 0.04)',
              activeBackground: 'rgba(71, 85, 105, 0.08)',
              color: '#475569',
            },
          },
        },
      },
    },
    card: {
      root: {
        background:
          'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%)',
        borderRadius: '0.75rem',
        color: '#1e293b',
        shadow:
          '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)',
      },
      body: {
        padding: '1.5rem',
      },
      title: {
        fontSize: '1.25rem',
        fontWeight: '600',
      },
      subtitle: {
        color: '#64748b',
      },
    },
    panel: {
      header: {
        background: '#f8fafc',
        borderColor: '#e2e8f0',
        color: '#1e293b',
        padding: '1rem 1.25rem',
        borderRadius: '0.5rem',
        fontWeight: '500',
      },
      content: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        color: '#475569',
        padding: '1.25rem',
      },
    },
    inputtext: {
      root: {
        background: '#ffffff',
        disabledBackground: '#f8fafc',
        filledBackground: '#f1f5f9',
        filledHoverBackground: '#e2e8f0',
        filledFocusBackground: '#ffffff',
        borderColor: '#e2e8f0',
        hoverBorderColor: '#cbd5e1',
        focusBorderColor: '#5521be',
        invalidBorderColor: '#ef4444',
        color: '#1e293b',
        disabledColor: '#64748b',
        placeholderColor: '#94a3b8',
        shadow: 'none',
        paddingX: '0.75rem',
        paddingY: '0.75rem',
        borderRadius: '0.5rem',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        transitionDuration: '0.2s',
      },
    },
    checkbox: {
      root: {
        borderRadius: '0.375rem',
        width: '1.25rem',
        height: '1.25rem',
        background: '#ffffff',
        checkedBackground: '#5521be',
        checkedHoverBackground: '#4a1ca8',
        disabledBackground: '#f1f5f9',
        filledBackground: '#ffffff',
        borderColor: '#cbd5e0',
        hoverBorderColor: '#5521be',
        focusBorderColor: '#5521be',
        checkedBorderColor: '#5521be',
        checkedHoverBorderColor: '#4a1ca8',
        checkedFocusBorderColor: '#5521be',
        checkedDisabledBorderColor: '#cbd5e0',
        invalidBorderColor: '#ef4444',
        shadow: 'none',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        transitionDuration: '0.2s',
      },
      icon: {
        color: '#ffffff',
        checkedColor: '#ffffff',
        checkedHoverColor: '#ffffff',
        disabledColor: '#94a3b8',
        size: '0.75rem',
      },
    },
    dropdown: {
      root: {
        background: '#ffffff',
        disabledBackground: '#f8fafc',
        borderColor: '#e2e8f0',
        hoverBorderColor: '#cbd5e1',
        focusBorderColor: '#5521be',
        invalidBorderColor: '#ef4444',
        color: '#1e293b',
        disabledColor: '#64748b',
        placeholderColor: '#94a3b8',
        shadow: 'none',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        transitionDuration: '0.2s',
      },
      overlay: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        borderRadius: '0.5rem',
        color: '#1e293b',
        shadow:
          '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
    },
    menu: {
      root: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        borderRadius: '0.5rem',
        color: '#1e293b',
        shadow:
          '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        transitionDuration: '0.15s',
      },
      item: {
        focusBackground: '#f1f5f9',
        activeBackground: '#5521be',
        color: '#475569',
        focusColor: '#1e293b',
        activeColor: '#ffffff',
        padding: '0.75rem 1rem',
        borderRadius: '0.375rem',
      },
    },
    menubar: {
      root: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        borderRadius: '0.5rem',
        color: '#1e293b',
        gap: '0.5rem',
        padding: '0.75rem',
      },
    },
    toast: {
      root: {
        borderRadius: '0.75rem',
        borderWidth: '0',
        transitionDuration: '0.3s',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        shadow:
          '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        backdropFilter: 'blur(10px)',
        borderLeft: '4px solid',
      },
      icon: {
        size: '1.25rem',
      },
      content: {
        padding: '1.25rem',
        gap: '1rem',
      },
      text: {
        gap: '0.5rem',
      },
      summary: {
        fontWeight: '600',
        fontSize: '0.9rem',
        lineHeight: '1.4',
      },
      detail: {
        fontSize: '0.85rem',
        lineHeight: '1.5',
        opacity: '0.9',
      },
      closeButton: {
        width: '1.75rem',
        height: '1.75rem',
        borderRadius: '0.375rem',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow:
            '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(85, 33, 190, 0.2)',
        },
        transitionDuration: '0.2s',
      },
      closeIcon: {
        size: '1rem',
      },
      colorScheme: {
        light: {
          info: {
            background:
              'linear-gradient(135deg, rgba(14, 165, 233, 0.95) 0%, rgba(59, 130, 246, 0.95) 100%)',
            borderColor: '#0ea5e9',
            color: '#ffffff',
            detailColor: 'rgba(255, 255, 255, 0.9)',
            shadow:
              '0 10px 25px -5px rgba(14, 165, 233, 0.25), 0 10px 10px -5px rgba(14, 165, 233, 0.1)',
            closeButton: {
              hoverBackground: 'rgba(255, 255, 255, 0.2)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(14, 165, 233, 0.3)',
              },
            },
          },
          success: {
            background:
              'linear-gradient(135deg, rgba(16, 185, 129, 0.95) 0%, rgba(34, 197, 94, 0.95) 100%)',
            borderColor: '#10b981',
            color: '#ffffff',
            detailColor: 'rgba(255, 255, 255, 0.9)',
            shadow:
              '0 10px 25px -5px rgba(16, 185, 129, 0.25), 0 10px 10px -5px rgba(16, 185, 129, 0.1)',
            closeButton: {
              hoverBackground: 'rgba(255, 255, 255, 0.2)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(16, 185, 129, 0.3)',
              },
            },
          },
          warn: {
            background:
              'linear-gradient(135deg, rgba(245, 158, 11, 0.95) 0%, rgba(251, 191, 36, 0.95) 100%)',
            borderColor: '#f59e0b',
            color: '#ffffff',
            detailColor: 'rgba(255, 255, 255, 0.9)',
            shadow:
              '0 10px 25px -5px rgba(245, 158, 11, 0.25), 0 10px 10px -5px rgba(245, 158, 11, 0.1)',
            closeButton: {
              hoverBackground: 'rgba(255, 255, 255, 0.2)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(245, 158, 11, 0.3)',
              },
            },
          },
          error: {
            background:
              'linear-gradient(135deg, rgba(239, 68, 68, 0.95) 0%, rgba(220, 38, 38, 0.95) 100%)',
            borderColor: '#ef4444',
            color: '#ffffff',
            detailColor: 'rgba(255, 255, 255, 0.9)',
            shadow:
              '0 10px 25px -5px rgba(239, 68, 68, 0.25), 0 10px 10px -5px rgba(239, 68, 68, 0.1)',
            closeButton: {
              hoverBackground: 'rgba(255, 255, 255, 0.2)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(239, 68, 68, 0.3)',
              },
            },
          },
          secondary: {
            background: '#ffffff',
            borderColor: '#e2e8f0',
            color: '#1e293b',
            detailColor: '#64748b',
            shadow:
              '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            closeButton: {
              hoverBackground: 'rgba(0, 0, 0, 0.05)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(85, 33, 190, 0.2)',
              },
            },
          },
          contrast: {
            background:
              'linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.95) 100%)',
            borderColor: '#1e293b',
            color: '#ffffff',
            detailColor: 'rgba(255, 255, 255, 0.8)',
            shadow:
              '0 10px 25px -5px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.1)',
            closeButton: {
              hoverBackground: 'rgba(255, 255, 255, 0.1)',
              focusRing: {
                shadow:
                  '0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(85, 33, 190, 0.3)',
              },
            },
          },
        },
      },
    },
    dialog: {
      root: {
        background: '#ffffff',
        borderColor: 'rgba(85, 33, 190, 0.2)',
        color: '#1e293b',
        borderRadius: '1rem',
        shadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        borderWidth: '1px',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      },
      header: {
        background: 'linear-gradient(135deg, #5521be 0%, #e036af 100%)',
        borderColor: 'transparent',
        color: '#ffffff',
        padding: '1.5rem 2rem',
        borderRadius: '1rem 1rem 0 0',
        gap: '1rem',
      },
      title: {
        fontWeight: '600',
        fontSize: '1.125rem',
        lineHeight: '1.4',
      },
      content: {
        background: '#ffffff',
        color: '#475569',
        padding: '2rem',
        fontSize: '0.95rem',
        lineHeight: '1.6',
      },
      footer: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        color: '#475569',
        padding: '1.5rem 2rem',
        gap: '0.75rem',
        borderRadius: '0 0 1rem 1rem',
      },
      closeButton: {
        background: 'rgba(255, 255, 255, 0.2)',
        hoverBackground: 'rgba(255, 255, 255, 0.3)',
        activeBackground: 'rgba(255, 255, 255, 0.4)',
        borderColor: 'transparent',
        color: '#ffffff',
        hoverColor: '#ffffff',
        activeColor: '#ffffff',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 3px rgba(255, 255, 255, 0.3)',
        },
        borderRadius: '0.5rem',
        size: '2rem',
        transitionDuration: '0.2s',
      },
      closeIcon: {
        size: '1rem',
      },
      mask: {
        background: 'rgba(0, 0, 0, 0.6)',
        backdropFilter: 'blur(8px)',
      },
    },
    confirmdialog: {
      icon: {
        size: '1.5rem',
        color: '#5521be',
      },
      content: {
        gap: '1rem',
      },
    },
    select: {
      root: {
        background: '#ffffff',
        disabledBackground: '#f8fafc',
        borderColor: '#e2e8f0',
        hoverBorderColor: '#cbd5e1',
        focusBorderColor: '#5521be',
        invalidBorderColor: '#ef4444',
        color: '#1e293b',
        disabledColor: '#64748b',
        placeholderColor: '#94a3b8',
        shadow: 'none',
        paddingX: '0.75rem',
        paddingY: '0.75rem',
        borderRadius: '0.5rem',
        borderWidth: '1px',
        fontSize: '0.875rem',
        fontWeight: '400',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        transitionDuration: '0.2s',
      },
      dropdown: {
        width: '2.5rem',
        color: '#64748b',
        hoverColor: '#5521be',
        focusColor: '#5521be',
      },
      overlay: {
        background: '#ffffff',
        borderColor: '#e2e8f0',
        borderRadius: '0.5rem',
        color: '#1e293b',
        shadow:
          '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        borderWidth: '1px',
      },
      list: {
        padding: '0.5rem',
        gap: '0.125rem',
      },
      option: {
        focusBackground: 'rgba(85, 33, 190, 0.08)',
        selectedBackground: 'rgba(85, 33, 190, 0.12)',
        selectedFocusBackground: 'rgba(85, 33, 190, 0.16)',
        color: '#1e293b',
        focusColor: '#1e293b',
        selectedColor: '#5521be',
        selectedFocusColor: '#5521be',
        padding: '0.75rem 1rem',
        borderRadius: '0.375rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        border: 'none',
        transition: 'all 0.2s ease',
        icon: {
          color: '#64748b',
          focusColor: '#5521be',
          selectedColor: '#5521be',
          selectedFocusColor: '#5521be',
        },
      },
      optionGroup: {
        background: 'transparent',
        color: '#64748b',
        fontWeight: '600',
        fontSize: '0.75rem',
        padding: '0.75rem 1rem 0.25rem 1rem',
        textTransform: 'uppercase',
        letterSpacing: '0.05em',
      },
      clearIcon: {
        color: '#64748b',
        hoverColor: '#ef4444',
        focusColor: '#ef4444',
      },
      checkmark: {
        color: '#5521be',
        selectedColor: '#ffffff',
        selectedFocusColor: '#ffffff',
      },
      emptyMessage: {
        background: 'transparent',
        color: '#64748b',
        padding: '0.75rem 1rem',
        fontSize: '0.875rem',
        fontStyle: 'italic',
      },
      chip: {
        focusBackground: 'rgba(85, 33, 190, 0.1)',
        color: '#5521be',
        borderRadius: '0.375rem',
        fontSize: '0.75rem',
        fontWeight: '500',
        padding: '0.25rem 0.5rem',
        gap: '0.25rem',
        removeTokenIcon: {
          color: '#64748b',
          hoverColor: '#ef4444',
          focusColor: '#ef4444',
        },
      },
    },
    paginator: {
      root: {
        background: 'transparent',
        color: '#475569',
        borderRadius: '0.5rem',
        padding: '0.75rem 1rem',
        gap: '0.5rem',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      },
      firstPageButton: {
        background: 'transparent',
        borderColor: '#e2e8f0',
        color: '#64748b',
        hoverBackground: '#f1f5f9',
        hoverBorderColor: '#cbd5e1',
        hoverColor: '#1e293b',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        borderRadius: '0.375rem',
        padding: '0.5rem',
        size: '2.25rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        transitionDuration: '0.2s',
      },
      previousPageButton: {
        background: 'transparent',
        borderColor: '#e2e8f0',
        color: '#64748b',
        hoverBackground: '#f1f5f9',
        hoverBorderColor: '#cbd5e1',
        hoverColor: '#1e293b',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        borderRadius: '0.375rem',
        padding: '0.5rem',
        size: '2.25rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        transitionDuration: '0.2s',
      },
      nextPageButton: {
        background: 'transparent',
        borderColor: '#e2e8f0',
        color: '#64748b',
        hoverBackground: '#f1f5f9',
        hoverBorderColor: '#cbd5e1',
        hoverColor: '#1e293b',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        borderRadius: '0.375rem',
        padding: '0.5rem',
        size: '2.25rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        transitionDuration: '0.2s',
      },
      lastPageButton: {
        background: 'transparent',
        borderColor: '#e2e8f0',
        color: '#64748b',
        hoverBackground: '#f1f5f9',
        hoverBorderColor: '#cbd5e1',
        hoverColor: '#1e293b',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        borderRadius: '0.375rem',
        padding: '0.5rem',
        size: '2.25rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        transitionDuration: '0.2s',
      },
      pageButton: {
        background: 'transparent',
        borderColor: '#e2e8f0',
        color: '#64748b',
        hoverBackground: '#f1f5f9',
        hoverBorderColor: '#cbd5e1',
        hoverColor: '#1e293b',
        selectedBackground: '#5521be',
        selectedBorderColor: '#5521be',
        selectedColor: '#ffffff',
        selectedHoverBackground: '#4a1ca8',
        selectedHoverBorderColor: '#4a1ca8',
        selectedHoverColor: '#ffffff',
        focusRing: {
          width: '0',
          style: 'none',
          color: 'unset',
          offset: '0',
          shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
        },
        borderRadius: '0.375rem',
        padding: '0.5rem',
        size: '2.25rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        transitionDuration: '0.2s',
      },
      rowPerPageDropdown: {
        root: {
          background: '#ffffff',
          borderColor: '#e2e8f0',
          hoverBorderColor: '#cbd5e1',
          focusBorderColor: '#5521be',
          color: '#1e293b',
          placeholderColor: '#94a3b8',
          shadow: 'none',
          paddingX: '0.75rem',
          paddingY: '0.5rem',
          borderRadius: '0.375rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          fontFamily:
            'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          focusRing: {
            width: '0',
            style: 'none',
            color: 'unset',
            offset: '0',
            shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
          },
          transitionDuration: '0.2s',
        },
      },
      jumpToPageDropdown: {
        root: {
          background: '#ffffff',
          borderColor: '#e2e8f0',
          hoverBorderColor: '#cbd5e1',
          focusBorderColor: '#5521be',
          color: '#1e293b',
          placeholderColor: '#94a3b8',
          shadow: 'none',
          paddingX: '0.75rem',
          paddingY: '0.5rem',
          borderRadius: '0.375rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          fontFamily:
            'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          focusRing: {
            width: '0',
            style: 'none',
            color: 'unset',
            offset: '0',
            shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
          },
          transitionDuration: '0.2s',
        },
      },
      jumpToPageInput: {
        root: {
          background: '#ffffff',
          borderColor: '#e2e8f0',
          hoverBorderColor: '#cbd5e1',
          focusBorderColor: '#5521be',
          color: '#1e293b',
          placeholderColor: '#94a3b8',
          shadow: 'none',
          paddingX: '0.75rem',
          paddingY: '0.5rem',
          borderRadius: '0.375rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          fontFamily:
            'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          focusRing: {
            width: '0',
            style: 'none',
            color: 'unset',
            offset: '0',
            shadow: '0 0 0 0.2rem rgba(85, 33, 190, 0.2)',
          },
          transitionDuration: '0.2s',
        },
      },
      current: {
        background: 'transparent',
        color: '#1e293b',
        fontSize: '0.875rem',
        fontWeight: '500',
        fontFamily:
          'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        padding: '0.5rem 0.75rem',
      },
    },
  },
});

export default ChainmaticTheme;
