import { Injectable } from '@angular/core';
import { Observable, from, map, catchError, of, combineLatest } from 'rxjs';
import { SupabaseService, PortalFeatureExternalConfigService } from '../../../core';
import { PortalFeature } from '../../../core/types';
import { 
  FacebookAccountOption, 
  CreativesUploaderExternalConfig, 
  CreativesUploaderConfigDialogData 
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class CreativesUploaderConfigService {
  constructor(
    private supabaseService: SupabaseService,
    private externalConfigService: PortalFeatureExternalConfigService,
  ) {}

  /**
   * Get all available Facebook accounts from fbam_facebook_ad_accounts
   */
  getAvailableFacebookAccounts(): Observable<FacebookAccountOption[]> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_ad_accounts')
        .select(`
          id,
          ad_account_id,
          ad_account_name,
          account_status,
          currency,
          is_accessible,
          fbam_facebook_users!inner(
            name,
            fbam_facebook_apps!inner(
              name
            )
          )
        `)
        .eq('is_accessible', true)
        .order('ad_account_name')
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching Facebook accounts:', response.error);
          return [];
        }
        
        return (response.data || []).map((account: any) => ({
          id: account.id,
          ad_account_id: account.ad_account_id,
          ad_account_name: account.ad_account_name,
          app_name: account.fbam_facebook_users.fbam_facebook_apps.name,
          user_name: account.fbam_facebook_users.name,
          account_status: account.account_status,
          currency: account.currency,
          is_accessible: account.is_accessible,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching Facebook accounts:', error);
        return of([]);
      })
    );
  }

  /**
   * Get current Creatives Uploader configuration
   */
  getCurrentConfig(): Observable<CreativesUploaderExternalConfig | null> {
    return this.externalConfigService.getFeatureExternalConfig(PortalFeature.CREATIVES_UPLOADER).pipe(
      map((config) => {
        if (!config?.config) {
          return null;
        }
        return config.config as CreativesUploaderExternalConfig;
      }),
      catchError((error) => {
        console.error('Error fetching Creatives Uploader config:', error);
        return of(null);
      })
    );
  }

  /**
   * Get dialog data (available accounts + current config)
   */
  getConfigDialogData(): Observable<CreativesUploaderConfigDialogData> {
    return combineLatest([
      this.getAvailableFacebookAccounts(),
      this.getCurrentConfig()
    ]).pipe(
      map(([availableAccounts, currentConfig]) => ({
        availableAccounts,
        currentConfig
      }))
    );
  }

  /**
   * Save Creatives Uploader configuration
   */
  saveConfig(config: CreativesUploaderExternalConfig): Observable<void> {
    return this.externalConfigService.upsertFeatureExternalConfig(
      PortalFeature.CREATIVES_UPLOADER,
      config
    ).pipe(
      map(() => void 0),
      catchError((error) => {
        console.error('Error saving Creatives Uploader config:', error);
        throw error;
      })
    );
  }
}
