/* Import shared styles */
@import '../../shared/styles/page-layout.css';

/* Component specific overrides */
.page-container {
  /* Back to normal layout */
}

/* Header styles are now handled by chm-feature-header component */

/* Facebook Account Column Styles */
.fb-account-text {
  font-family: monospace;
  font-size: 0.875rem;
  color: #374151;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

.no-account-text {
  color: #9ca3af;
  font-style: italic;
}

/* Active Uploads Indicator */
.active-uploads-indicator .active-uploads-tag {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.active-uploads-indicator .active-uploads-tag i {
  margin-right: 0.5rem;
}

/* Filters specific to creatives uploader */
.filters-grid {
  grid-template-columns: 200px 200px 1fr 150px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-item.search-item {
  min-width: 250px;
}

/* Fix search input height and icon positioning */
.search-input {
  height: 42px !important;
  line-height: 1.5 !important;
}

.p-input-icon-left {
  position: relative;
}

.p-input-icon-left > i {
  position: absolute;
  top: 50%;
  left: 0.75rem;
  transform: translateY(-50%);
  z-index: 2;
  color: #9ca3af;
  transition: color 0.3s ease;
  font-size: 0.875rem;
}

.p-input-icon-left:hover > i,
.p-input-icon-left:focus-within > i {
  color: #5521be;
}

/* Create proper stacking context hierarchy */
.filters-section {
  position: relative;
  z-index: 100;
}

.filter-card {
  position: relative;
  z-index: 100;
}

/* Stats cards should have lower z-index */
.stats-grid {
  position: relative;
  z-index: 10;
}

.stat-card {
  position: relative;
  z-index: 10;
}

/* Table section normal layout */
.table-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* Fix select dropdown z-index with proper selectors */
::ng-deep .p-select-list-container {
  z-index: 1000 !important;
}

::ng-deep .p-select-panel {
  z-index: 1000 !important;
}

::ng-deep .p-select-overlay {
  z-index: 1000 !important;
}

::ng-deep .p-component-overlay {
  z-index: 1000 !important;
}

::ng-deep .p-overlay {
  z-index: 1000 !important;
}

/* Simple pagination dropdown fix */
::ng-deep .custom-paginator .p-dropdown-panel,
::ng-deep .custom-paginator .p-select-panel {
  z-index: 9999 !important;
}

/* Ensure dropdown trigger has proper positioning */
::ng-deep .custom-paginator .p-dropdown,
::ng-deep .custom-paginator .p-select {
  position: relative !important;
  z-index: 100 !important;
}

/* Smart table overflow - only pagination area visible */
::ng-deep .page-container .p-datatable-wrapper {
  overflow-x: auto !important;
  overflow-y: hidden !important;
}

::ng-deep .page-container .p-datatable .p-datatable-footer {
  overflow: visible !important;
  position: relative !important;
  z-index: 100 !important;
}

::ng-deep .page-container .p-paginator {
  overflow: visible !important;
  position: relative !important;
  z-index: 100 !important;
}


/* Type column styling */
.type-cell {
  text-align: center;
  width: 60px;
}

.file-type-icon {
  font-size: 1.5rem;
  transition: all 0.2s ease;
}

/* Enhanced file type colors */
.file-type-video {
  color: #ef4444;
  text-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.file-type-image {
  color: #10b981;
  text-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.file-type-other {
  color: #6b7280;
  text-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

/* Hover effects for file type icons */
.file-type-video:hover {
  color: #dc2626;
  transform: scale(1.1);
}

.file-type-image:hover {
  color: #059669;
  transform: scale(1.1);
}

.file-type-other:hover {
  color: #4b5563;
  transform: scale(1.1);
}

/* Actions column styling */
.actions-cell {
  text-align: center;
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

.action-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #6b7280;
}

.action-link:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-link.facebook {
  background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
  color: white;
}

.action-link.facebook:hover {
  background: linear-gradient(135deg, #166fe5 0%, #1976d2 100%);
}

.action-link.google-drive {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
}

.action-link.google-drive:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2e7d32 100%);
}

.action-link i {
  font-size: 0.875rem;
}

/* Beautiful status tags with custom colors - higher specificity */
::ng-deep .creatives-uploader-container .status-tag,
::ng-deep .page-container .status-tag {
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  border-radius: 12px !important;
  padding: 0.5rem 0.75rem !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  border: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Pending - Purple gradient */
::ng-deep .creatives-uploader-container .p-tag-secondary,
::ng-deep .page-container .p-tag-secondary,
::ng-deep .p-tag.p-tag-secondary {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
  color: white !important;
}

/* Downloading - Blue gradient */
::ng-deep .creatives-uploader-container .p-tag-info,
::ng-deep .page-container .p-tag-info,
::ng-deep .p-tag.p-tag-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  color: white !important;
}

/* Uploading - Orange gradient */
::ng-deep .creatives-uploader-container .p-tag-warning,
::ng-deep .page-container .p-tag-warning,
::ng-deep .p-tag.p-tag-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  color: white !important;
}

/* Uploaded - Green gradient */
::ng-deep .creatives-uploader-container .p-tag-success,
::ng-deep .page-container .p-tag-success,
::ng-deep .p-tag.p-tag-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  color: white !important;
}

/* Failed - Red gradient */
::ng-deep .creatives-uploader-container .p-tag-danger,
::ng-deep .page-container .p-tag-danger,
::ng-deep .p-tag.p-tag-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  color: white !important;
}

/* Status spinner styling */
.status-spinner {
  font-size: 0.75rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects for status tags */
::ng-deep .creatives-uploader-container .status-tag:hover,
::ng-deep .page-container .status-tag:hover,
::ng-deep .p-tag:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* Enhanced table cell styling */
.file-name-cell .file-name {
  font-weight: 600;
  color: #1f2937;
  display: block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.file-name-cell .file-name:hover {
  color: #5521be;
}

.file-name-cell .error-message {
  color: #ef4444;
  font-size: 0.75rem;
  display: block;
  margin-top: 0.25rem;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  background: rgba(239, 68, 68, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border-left: 3px solid #ef4444;
}

/* Enhanced file size styling */
.file-size {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
  background: rgba(107, 114, 128, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

/* Enhanced date styling */
.date-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
