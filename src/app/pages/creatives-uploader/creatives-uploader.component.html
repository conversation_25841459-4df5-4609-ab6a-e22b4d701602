<div class="page-container">
  <!-- Header -->
  <chm-feature-header
    (configClick)="openConfigDialog()"
    [showConfigButton]="true"
    description="Manage your Google Drive to Facebook creative uploads"
    iconClass="pi pi-cloud-upload"
    title="Creative Files Upload">
  </chm-feature-header>

  <!-- Active Uploads Indicator -->
  <div *ngIf="hasActiveUploads()" class="active-uploads-indicator">
    <p-tag
      [value]="getActiveUploadsCount() + ' Active Uploads'"
      severity="info"
      styleClass="active-uploads-tag">
      <i class="pi pi-spin pi-spinner"></i>
    </p-tag>
  </div>
</div>

<!-- Filters Section -->
<div class="filters-section">
  <div class="filter-card">
    <div class="filters-grid">
      <!-- Status Filter -->
      <div class="filter-group">
        <label class="filter-label">Status</label>
        <p-select
          (onChange)="onStatusFilterChange($event.value)"
          [options]="statusOptions"
          [showClear]="true"
          optionLabel="label"
          optionValue="value"
          placeholder="All Statuses"
          styleClass="filter-dropdown">
        </p-select>
      </div>

      <!-- File Type Filter -->
      <div class="filter-group">
        <label class="filter-label">File Type</label>
        <p-select
          (onChange)="onMimeTypeFilterChange($event.value)"
          [options]="mimeTypeOptions"
          optionLabel="label"
          optionValue="value"
          placeholder="All Files"
          styleClass="filter-dropdown">
        </p-select>
      </div>

      <!-- Search -->
      <div class="filter-group search-item">
        <label class="filter-label">Search</label>
        <span class="p-input-icon-left">
          <i class="pi pi-search"></i>
          <input
            (input)="onSearchChange($event)"
            class="search-input"
            pInputText
            placeholder="Search by filename..."
            type="text">
        </span>
      </div>

      <!-- Refresh Button -->
      <div class="filter-group">
        <label class="filter-label">&nbsp;</label>
        <p-button
          (onClick)="loadFiles()"
          [loading]="loading"
          icon="pi pi-refresh"
          label="Refresh"
          severity="secondary"
          styleClass="refresh-button">
        </p-button>
      </div>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div *ngIf="!loading" class="stats-grid">
  <div class="stat-card">
    <div class="stat-icon">
      <i class="pi pi-file"></i>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ totalRecords }}</div>
      <div class="stat-label">Total Files</div>
    </div>
  </div>

  <div class="stat-card">
    <div class="stat-icon active">
      <i class="pi pi-clock"></i>
    </div>
    <div class="stat-content">
      <div class="stat-value">{{ getActiveUploadsCount() }}</div>
      <div class="stat-label">Active Uploads</div>
    </div>
  </div>
</div>

<!-- Files Table -->
<div class="table-section">
  <p-table
    #dt
    [globalFilterFields]="['file_name']"
    [loading]="loading"
    [paginatorStyleClass]="'custom-paginator'"
    [paginator]="true"
    [rowsPerPageOptions]="[10, 20, 50]"
    [rows]="20"
    [showCurrentPageReport]="true"
    [showGridlines]="true"
    [tableStyle]="{'width': '100%'}"
    [value]="files"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    stripedRows="true">


    <!-- Table Header -->
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 60px;">Type</th>
        <th>File Name</th>
        <th style="width: 100px;">Size</th>
        <th style="width: 150px;">Status</th>
        <th style="width: 180px;">Facebook Account</th>
        <th style="width: 150px;">Created</th>
        <th style="width: 150px;">Uploaded</th>
        <th style="width: 100px;">Actions</th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template let-file pTemplate="body">
      <tr>
        <!-- File Type Icon -->
        <td class="type-cell">
          <i
            [class]="'pi ' + getMimeTypeIcon(file.mime_type) + ' ' + getFileTypeClass(file.mime_type) + ' file-type-icon'"
            [title]="getMimeTypeLabel(file.mime_type)">
          </i>
        </td>

        <!-- File Name -->
        <td>
          <div class="file-name-cell">
            <span [title]="file.file_name" class="file-name">{{ file.file_name }}</span>
            <small *ngIf="file.upload_error" class="error-message">
              {{ file.upload_error }}
            </small>
          </div>
        </td>

        <!-- File Size -->
        <td>
          <span class="file-size">{{ formatFileSize(file.file_size) }}</span>
        </td>

        <!-- Status -->
        <td>
          <p-tag
            [severity]="getStatusSeverity(file.status)"
            [value]="getStatusLabel(file.status)"
            styleClass="status-tag">
            <i *ngIf="isActiveStatus(file.status)" class="pi pi-spin pi-spinner status-spinner"></i>
          </p-tag>
        </td>

        <!-- Facebook Account -->
        <td>
          <span class="fb-account-text" *ngIf="file.fb_account_id; else noAccount">
            {{ file.fb_account_id }}
          </span>
          <ng-template #noAccount>
            <span class="no-account-text">-</span>
          </ng-template>
        </td>

        <!-- Created Date -->
        <td>
          <span class="date-text">{{ formatDate(file.created_at) }}</span>
        </td>

        <!-- Uploaded Date -->
        <td>
          <span class="date-text">{{ file.uploaded_at ? formatDate(file.uploaded_at) : '-' }}</span>
        </td>

        <!-- Actions -->
        <td class="actions-cell">
          <div class="action-buttons">
            <!-- Facebook Creative Link -->
            <a
              *ngIf="file.fb_creative_id"
              [href]="getFacebookCreativeUrl(file.fb_creative_id)"
              class="action-link facebook"
              target="_blank"
              title="View in Facebook Ads Manager">
              <i class="fab fa-facebook-f"></i>
            </a>

            <!-- Google Drive Link -->
            <a
              [href]="getGoogleDriveUrl(file.google_drive_file_id)"
              class="action-link google-drive"
              target="_blank"
              title="View in Google Drive">
              <i class="fab fa-google-drive"></i>
            </a>
          </div>
        </td>


      </tr>
    </ng-template>

    <!-- Empty State -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8">
          <div class="empty-state">
            <i class="pi pi-cloud-upload empty-icon"></i>
            <h3>No files found</h3>
            <p>No creative files match your current filters.</p>
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Loading Template -->
    <ng-template pTemplate="loadingbody">
      <tr>
        <td colspan="8">
          <div class="loading-state">
            <p-progressSpinner strokeWidth="3"></p-progressSpinner>
            <p>Loading files...</p>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<!-- Configuration Dialog -->
<app-creatives-uploader-config-dialog
  (result)="onConfigResult($event)"
  [dialogData]="configDialogData"
  [visible]="showConfigDialog">
</app-creatives-uploader-config-dialog>

<!-- Toast Messages -->
<p-toast></p-toast>
