export interface FacebookAccountOption {
  id: string;
  ad_account_id: string;
  ad_account_name: string;
  app_name: string;
  user_name: string;
  account_status: number;
  currency: string;
  is_accessible: boolean;
}

export interface AccountKeywordMapping {
  ad_account_id: string;
  description_keyword: string;
}

export interface CreativesUploaderExternalConfig {
  accountKeywords: AccountKeywordMapping[]; // Array of account-keyword mappings
}

export interface CreativesUploaderConfigDialogData {
  availableAccounts: FacebookAccountOption[];
  currentConfig: CreativesUploaderExternalConfig | null;
}

export interface CreativesUploaderConfigResult {
  config: CreativesUploaderExternalConfig;
  confirmed: boolean;
}
