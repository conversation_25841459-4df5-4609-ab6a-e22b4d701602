import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '../../../core';
import { FacebookAdAccount } from '../models';

@Injectable({
  providedIn: 'root',
})
export class FacebookWebhookService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Get Facebook ad accounts for a specific user
   * GET /webhook/fb-accounts-manager/accounts?user_id={userId}
   */
  getAccountsForUser(userId: string): Observable<FacebookAdAccount[]> {
    const url = `${this.configService.n8nBaseUrl}/webhook/fb-accounts-manager/accounts`;
    const params = new HttpParams().set('user_id', userId);
    return this.http.get<FacebookAdAccount[]>(url, { params });
  }
}
