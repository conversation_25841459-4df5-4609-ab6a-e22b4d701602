import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { AdWinnersWebhookService } from '../../services';

@Component({
  selector: 'chm-media-modal',
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule],
  template: `
    <p-dialog
      [(visible)]="visible"
      (onHide)="onClose()"
      [modal]="true"
      [closable]="true"
      [draggable]="false"
      [resizable]="false"
      [maximizable]="true"
      styleClass="media-modal"
      [style]="{ width: '90vw', maxWidth: '1200px' }"
    >
      <ng-template pTemplate="header">
        <div class="modal-header">
          <h3>{{ title || 'Media Preview' }}</h3>
        </div>
      </ng-template>

      <div class="media-content">
        <!-- Video -->
        <video
          *ngIf="videoUrl && !videoError"
          [src]="videoUrl"
          class="modal-video"
          [controls]="true"
          [autoplay]="true"
          [muted]="false"
          (error)="onVideoError($event)"
        ></video>

        <!-- Image -->
        <img
          *ngIf="(!videoUrl || videoError) && imageUrl && !imageError"
          [src]="imageUrl"
          [alt]="title"
          class="modal-image"
          (error)="onImageError()"
        />

        <!-- Error message -->
        <div
          *ngIf="(videoError && imageError) || (!videoUrl && !imageUrl)"
          class="error-message"
        >
          <i class="pi pi-exclamation-triangle"></i>
          <p>Media could not be loaded</p>
        </div>
      </div>

      <!-- Footer buttons inside dialog content -->
      <div class="modal-footer">
        <p-button
          label="Close"
          icon="pi pi-times"
          (onClick)="onClose()"
          severity="secondary"
        >
        </p-button>
        <div class="footer-actions">
          <p-button
            *ngIf="videoUrl || imageUrl"
            label="Open Media"
            icon="pi pi-external-link"
            (onClick)="openInNewTab()"
            [outlined]="true"
            severity="secondary"
          >
          </p-button>
          <p-button
            *ngIf="adId"
            label="View on Facebook"
            icon="pi pi-facebook"
            (onClick)="openFacebookAd()"
            severity="primary"
          >
          </p-button>
        </div>
      </div>
    </p-dialog>
  `,
  styles: [
    `
      :host ::ng-deep .media-modal .p-dialog-content {
        padding: 0;
        overflow: hidden;
      }

      :host ::ng-deep .media-modal .p-dialog-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .modal-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .media-content {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        max-height: 70vh;
        background: #000;
        position: relative;
      }

      .modal-video {
        width: 100%;
        height: auto;
        max-height: 70vh;
        object-fit: contain;
      }

      .modal-image {
        width: 100%;
        height: auto;
        max-height: 70vh;
        object-fit: contain;
      }

      .error-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #64748b;
        padding: 2rem;
      }

      .error-message i {
        font-size: 3rem;
        color: #ef4444;
      }

      .error-message p {
        margin: 0;
        font-size: 1.1rem;
      }

      .modal-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        border-top: 1px solid #e2e8f0;
        background: white;
      }

      .footer-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
      }

      /* Responsive */
      @media (max-width: 768px) {
        :host ::ng-deep .media-modal {
          width: 95vw !important;
        }

        .media-content {
          min-height: 300px;
          max-height: 60vh;
        }

        .modal-footer {
          flex-direction: column;
          gap: 0.75rem;
        }

        .footer-actions {
          flex-direction: column;
          width: 100%;
          gap: 0.5rem;
        }

        .footer-actions p-button {
          width: 100%;
        }
      }
    `,
  ],
})
export class MediaModalComponent {
  @Input() visible: boolean = false;
  @Input() videoUrl: string | null = null;
  @Input() imageUrl: string | null = null;
  @Input() title: string = '';
  @Input() adId: string | null = null;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() videoUrlRefreshed = new EventEmitter<string>();

  videoError = false;
  imageError = false;

  constructor(private webhookService: AdWinnersWebhookService) {}

  onClose(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    this.resetErrors();
  }

  onVideoError(event: any): void {
    console.log('🎬 Video error event:', event);

    // Check if this is a 403 error (expired presigned URL) by making an HTTP status check
    if (this.videoUrl && this.adId) {
      this.checkVideoUrlStatus();
    } else {
      this.videoError = true;
    }
  }

  onImageError(): void {
    this.imageError = true;
  }

  /**
   * Refresh the video URL by calling the webhook
   */
  refreshVideoUrl(): void {
    if (!this.adId) {
      return;
    }

    this.webhookService.refreshVideoUrl(this.adId).subscribe({
      next: (response) => {
        console.log('✅ Video URL refresh successful:', response);

        // Update the video URL directly and clear error
        if (response.source) {
          this.videoUrl = response.source;
          this.videoError = false;

          // Emit event to parent component with new URL
          this.videoUrlRefreshed.emit(response.source);
        }
      },
      error: (error) => {
        console.error('❌ Video URL refresh failed:', error);
        // Keep video error state, no user notification
      },
    });
  }

  openInNewTab(): void {
    const url = this.videoUrl || this.imageUrl;
    if (url) {
      window.open(url, '_blank');
    }
  }

  openFacebookAd(): void {
    if (this.adId) {
      const facebookUrl = this.generateFacebookAdUrl(this.adId);
      window.open(facebookUrl, '_blank');
    }
  }

  generateFacebookAdUrl(adId: string): string {
    // Facebook Ads Manager Ad Preview URL format
    return `https://www.facebook.com/adsmanager/manage/adpreview/?ad_id=${adId}`;
  }

  /**
   * Check the HTTP status of the video URL to determine if it's a 403 error
   */
  private checkVideoUrlStatus(): void {
    if (!this.videoUrl) {
      this.videoError = true;
      return;
    }

    console.log('🔍 Checking video URL status:', this.videoUrl);

    this.webhookService.checkUrlStatus(this.videoUrl).subscribe({
      next: (statusResponse) => {
        console.log('📊 Video URL status check result:', statusResponse);

        if (statusResponse.is403Error && this.adId) {
          console.log(
            '🔄 Detected 403 error for video, attempting to refresh URL...',
          );
          this.refreshVideoUrl();
        } else if (statusResponse.isCorsError && this.adId) {
          console.log(
            '🔄 CORS error detected - assuming expired URL, attempting to refresh...',
          );
          // If we get a CORS error, it might be because the URL is expired
          // Try refreshing as a fallback
          this.refreshVideoUrl();
        } else if (!statusResponse.accessible) {
          console.log(
            '❌ Video URL is not accessible (status: ' + statusResponse.status + ')',
          );
          this.videoError = true;
        } else {
          // URL is accessible but video still failed to load - might be a different issue
          console.log(
            '⚠️ Video URL is accessible but video failed to load - different issue',
          );
          this.videoError = true;
        }
      },
      error: (error) => {
        console.error('❌ Failed to check video URL status:', error);
        // If we can't check the status and we have an adId, try refreshing as fallback
        if (this.adId) {
          console.log('🔄 Falling back to refresh attempt due to status check failure...');
          this.refreshVideoUrl();
        } else {
          this.videoError = true;
        }
      },
    });
  }

  private resetErrors(): void {
    this.videoError = false;
    this.imageError = false;
  }
}
