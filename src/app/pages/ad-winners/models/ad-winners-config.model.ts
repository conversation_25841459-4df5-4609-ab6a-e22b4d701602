export interface FacebookAccountOption {
  id: string;
  ad_account_id: string;
  ad_account_name: string;
  app_name: string;
  user_name: string;
  account_status: number;
  currency: string;
  is_accessible: boolean;
}

export interface AdWinnersExternalConfig {
  selectedAccounts: string[]; // Array of ad_account_ids
}

export interface AdWinnersConfigDialogData {
  availableAccounts: FacebookAccountOption[];
  currentConfig: AdWinnersExternalConfig | null;
}

export interface AdWinnersConfigResult {
  config: AdWinnersExternalConfig;
  confirmed: boolean;
}
