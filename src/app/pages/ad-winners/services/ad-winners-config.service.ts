import { Injectable } from '@angular/core';
import { Observable, from, map, catchError, of, combineLatest } from 'rxjs';
import { SupabaseService, PortalFeatureExternalConfigService } from '../../../core';
import { PortalFeature } from '../../../core/types';
import {
  FacebookAccountOption,
  AdWinnersExternalConfig,
  AdWinnersConfigDialogData
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class AdWinnersConfigService {
  constructor(
    private supabaseService: SupabaseService,
    private externalConfigService: PortalFeatureExternalConfigService,
  ) {}

  /**
   * Get all available Facebook accounts from fbam_facebook_ad_accounts
   */
  getAvailableFacebookAccounts(): Observable<FacebookAccountOption[]> {
    return from(
      this.supabaseService.client
        .from('fbam_facebook_ad_accounts')
        .select(`
          id,
          ad_account_id,
          ad_account_name,
          account_status,
          currency,
          is_accessible,
          fbam_facebook_users!inner(
            name,
            fbam_facebook_apps!inner(
              name
            )
          )
        `)
        .eq('is_accessible', true)
        .order('ad_account_name')
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching Facebook accounts:', response.error);
          return [];
        }

        return (response.data || []).map((account: any) => ({
          id: account.id,
          ad_account_id: account.ad_account_id,
          ad_account_name: account.ad_account_name,
          app_name: account.fbam_facebook_users.fbam_facebook_apps.name,
          user_name: account.fbam_facebook_users.name,
          account_status: account.account_status,
          currency: account.currency,
          is_accessible: account.is_accessible,
        }));
      }),
      catchError((error) => {
        console.error('Error fetching Facebook accounts:', error);
        return of([]);
      })
    );
  }

  /**
   * Get current Ad Winners configuration
   */
  getCurrentConfig(): Observable<AdWinnersExternalConfig | null> {
    return this.externalConfigService.getFeatureExternalConfig(PortalFeature.AD_WINNERS).pipe(
      map((config) => {
        if (!config?.config) {
          return null;
        }
        return config.config as AdWinnersExternalConfig;
      }),
      catchError((error) => {
        console.error('Error fetching Ad Winners config:', error);
        return of(null);
      })
    );
  }

  /**
   * Get dialog data (available accounts + current config)
   */
  getConfigDialogData(): Observable<AdWinnersConfigDialogData> {
    return combineLatest([
      this.getAvailableFacebookAccounts(),
      this.getCurrentConfig()
    ]).pipe(
      map(([availableAccounts, currentConfig]) => ({
        availableAccounts,
        currentConfig
      }))
    );
  }

  /**
   * Save Ad Winners configuration
   */
  saveConfig(config: AdWinnersExternalConfig): Observable<void> {
    return this.externalConfigService.upsertFeatureExternalConfig(
      PortalFeature.AD_WINNERS,
      config
    ).pipe(
      map(() => void 0),
      catchError((error) => {
        console.error('Error saving Ad Winners config:', error);
        throw error;
      })
    );
  }
}
