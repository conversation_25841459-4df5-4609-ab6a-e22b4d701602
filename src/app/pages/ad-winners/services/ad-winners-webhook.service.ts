import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ConfigService } from '../../../core';
import {
  UrlStatusCheckResponse,
  VideoRefreshRequest,
  VideoRefreshResponse,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class AdWinnersWebhookService {
  constructor(
    private http: HttpClient,
    private configService: ConfigService,
  ) {}

  /**
   * Check the HTTP status of a URL to determine if it's accessible
   * Note: This may fail due to CORS restrictions for external URLs
   */
  checkUrlStatus(url: string): Observable<UrlStatusCheckResponse> {
    return this.http
      .head(url, {
        observe: 'response',
        // Add headers to handle CORS better
        headers: {
          'Access-Control-Allow-Origin': '*',
        },
      })
      .pipe(
        map((response) => ({
          url,
          status: response.status,
          accessible: response.status >= 200 && response.status < 300,
          is403Error: response.status === 403,
        })),
        catchError((error: HttpErrorResponse) => {
          console.log('🔍 HTTP status check error:', error);

          // Handle CORS errors and other network issues
          const isCorsError =
            error.status === 0 && error.error instanceof ProgressEvent;
          const is403Error = error.status === 403;

          return of({
            url,
            status: error.status || 0,
            accessible: false,
            is403Error,
            isCorsError,
            error: error.message,
          });
        }),
      );
  }

  /**
   * Refresh expired video URL by calling n8n webhook
   */
  refreshVideoUrl(adId: string): Observable<VideoRefreshResponse> {
    const url = `${this.configService.n8nBaseUrl}/webhook/ad-winners/ads/video/refresh`;
    const payload: VideoRefreshRequest = { ad_id: adId };
    return this.http.post<VideoRefreshResponse>(url, payload);
  }
}
