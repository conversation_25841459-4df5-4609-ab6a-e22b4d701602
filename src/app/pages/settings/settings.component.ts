import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { FeatureHeaderComponent } from '../../shared';

@Component({
  selector: 'chm-settings',
  standalone: true,
  imports: [CommonModule, RouterOutlet, FeatureHeaderComponent],
  template: `
    <div class="settings-container">
      <chm-feature-header
        title="Settings"
        subtitle="Configure your platform settings"
        icon="pi pi-cog">
      </chm-feature-header>
      
      <div class="settings-content">
        <router-outlet></router-outlet>
      </div>
    </div>
  `,
  styles: [`
    .settings-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .settings-content {
      flex: 1;
      padding: 1.5rem;
      overflow-y: auto;
    }
  `]
})
export class SettingsComponent {}
