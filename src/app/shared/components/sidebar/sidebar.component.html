<!-- Mobile Overlay -->
<div (click)="closeMobileSidebar()" *ngIf="isMobileOpen" class="mobile-overlay"></div>

<div [class.collapsed]="isCollapsed" [class.mobile-open]="isMobileOpen" class="sidebar">
  <!-- Header with hamburger -->
  <div class="sidebar-header">
    <div *ngIf="!isCollapsed" class="logo">
      <img alt="Chainmatic" class="logo-image" src="images/chainmatic-portal.png">
    </div>
    <p-button
      (onClick)="toggleSidebar()"
      [text]="true"
      class="hamburger-btn"
      icon="pi pi-bars"
      size="small">
    </p-button>
  </div>

  <!-- Navigation Items -->
  <div class="nav-section">
    <!-- Regular Navigation Items -->
    <div class="nav-items">
      <div
        (click)="selectFeature(item)"
        *ngFor="let item of navItems"
        [class.active]="activeNav === item.id"
        class="nav-item">

        <!-- Icon -->
        <div [class.active-icon]="activeNav === item.id" class="nav-icon">
          <i [class]="item.icon"></i>
        </div>

        <!-- Name (only when expanded) -->
        <span *ngIf="!isCollapsed" class="nav-name">{{ item.name }}</span>
      </div>
    </div>

    <!-- Settings Section -->
    <div *ngIf="hasSettings" class="settings-section">
      <!-- Settings Header -->
      <div *ngIf="!isCollapsed" class="settings-header">
        <span class="settings-title">Settings</span>
      </div>

      <!-- Settings Items -->
      <div class="nav-items settings-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of settingsItems"
          [class.active]="activeNav === item.id"
          class="nav-item settings-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name (only when expanded) -->
          <span *ngIf="!isCollapsed" class="nav-name">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- User Section -->
  <div *ngIf="currentUser" class="user-section">
    <div *ngIf="!isCollapsed" class="user-info">
      <div class="user-avatar">
        <span>{{ getUserInitials() }}</span>
      </div>
      <div class="user-details">
        <span class="user-email">{{ currentUser.email }}</span>
      </div>
    </div>

    <div class="user-actions">
      <button
        (click)="logout()"
        [title]="isCollapsed ? 'Logout' : ''"
        class="logout-button">
        <i class="pi pi-sign-out"></i>
        <span *ngIf="!isCollapsed" class="logout-text">Logout</span>
      </button>
    </div>
  </div>
</div>
