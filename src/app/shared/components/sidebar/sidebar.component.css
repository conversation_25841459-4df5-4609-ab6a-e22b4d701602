/* Main Sidebar Container */
.sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
}

/* Nested Sidebar Container (Settings) */
.nested-sidebar-container {
  position: fixed;
  left: 280px; /* Position next to main sidebar */
  top: 0;
  height: 100vh;
  z-index: 999;
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* When main sidebar is collapsed, adjust nested sidebar position */
.nested-sidebar-container.main-collapsed {
  left: 70px;
}

.sidebar {
  height: 100vh;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.8) 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 4px 0 24px rgba(0, 0, 0, 0.1);
}

/* Main Sidebar */
.main-sidebar {
  width: 280px;
}

.main-sidebar.collapsed {
  width: 70px;
}

/* Second Sidebar (Settings) */
.second-sidebar {
  width: 280px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 236, 249, 0.9) 100%);
  box-shadow: 2px 0 24px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.second-sidebar.collapsed {
  width: 60px;
}

/* Compact navigation section for settings */
.nav-section.compact {
  padding: 1rem 0.5rem;
}

.nav-section.compact .nav-items {
  gap: 0.25rem;
}

@keyframes slideInRight {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Header */
.sidebar-header {
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.main-sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 1rem;
}

/* Settings Header in Second Sidebar */
.second-sidebar .sidebar-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.settings-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settings-header-icon {
  color: #5521be;
  font-size: 1.25rem;
}

.settings-header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.close-settings-btn {
  opacity: 0.7;
  transition: all 0.2s;
  border-radius: 8px !important;
}

.close-settings-btn:hover {
  opacity: 1;
  background: rgba(85, 33, 190, 0.1) !important;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 32px;
  width: auto;
  max-width: 180px;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease;
}

.logo-image:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  transform: scale(1.02);
}

.hamburger-btn {
  opacity: 0.7;
  transition: all 0.2s;
  border-radius: 8px !important;
}

.hamburger-btn:hover {
  opacity: 1;
  background: rgba(85, 33, 190, 0.1) !important;
}

/* Navigation Section */
.nav-section {
  flex: 1;
  padding: 1rem 0.75rem;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.nav-section::-webkit-scrollbar {
  width: 4px;
}

.nav-section::-webkit-scrollbar-track {
  background: transparent;
}

.nav-section::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

/* Settings Button */
.settings-button-container {
  margin-bottom: 1rem;
}

.settings-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
}

.settings-button .pi-cog {
  font-size: 1.125rem; /* Larger settings icon */
}

.settings-button:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(85, 33, 190, 0.1);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

.settings-button.active {
  background: rgba(85, 33, 190, 0.1);
  border-color: rgba(85, 33, 190, 0.2);
  color: #5521be;
  transform: translateX(2px);
}

.settings-chevron {
  margin-left: auto;
  font-size: 0.75rem;
  transition: transform 0.2s ease;
  opacity: 0.6;
}

.settings-button.active .settings-chevron {
  opacity: 1;
}

/* Subtle collapse arrow on the left */
.settings-button::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  border-right: 6px solid rgba(85, 33, 190, 0.3);
  opacity: 0;
  transition: all 0.2s ease;
}

.settings-button:hover::before {
  opacity: 0.6;
  left: -6px;
}

.settings-button.active::before {
  opacity: 0.8;
  border-right-color: #5521be;
}

/* Settings Menu (Second Layer) */
.settings-menu {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.settings-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

.settings-menu-item:hover {
  background: rgba(255, 255, 255, 0.6);
  color: #1e293b;
  transform: translateX(2px);
}

.settings-menu-item.active {
  background: rgba(85, 33, 190, 0.1);
  color: #5521be;
}

.settings-menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.settings-menu-icon.active-icon {
  color: #5521be;
}

.settings-menu-name {
  flex: 1;
}

/* Settings Navigation Items in Second Sidebar */
.settings-nav-item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.625rem 0.75rem; /* More compact padding */
  font-size: 0.8rem; /* Slightly smaller font */
}

.settings-nav-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(85, 33, 190, 0.15);
  transform: translateX(1px); /* Smaller hover effect */
}

.settings-nav-item.active {
  background: rgba(85, 33, 190, 0.1);
  border-color: rgba(85, 33, 190, 0.2);
  color: #5521be;
}

.settings-nav-item .nav-icon {
  width: 1rem;
  height: 1rem;
  font-size: 0.75rem;
}

.settings-nav-item .nav-name {
  font-weight: 500;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(85, 33, 190, 0.1);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.1) 0%, rgba(224, 54, 175, 0.05) 100%);
  border-color: rgba(85, 33, 190, 0.2);
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.15);
  border-left: 3px solid #5521be;
  color: #5521be;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0.75rem;
  background: transparent;
  border: none;
  box-shadow: none;
}

.sidebar.collapsed .nav-item:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: none;
  box-shadow: none;
}

.sidebar.collapsed .nav-item.active {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Nav Icon */
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
  color: #64748b;
  transition: all 0.2s ease;
}

.nav-icon i {
  font-size: 1rem;
}

/* Active icon styling */
.nav-icon.active-icon {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.3);
}

/* When collapsed, make active icon more prominent */
.sidebar.collapsed .nav-icon.active-icon {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.4);
  transform: scale(1.1);
}

/* Hover effect for icons when collapsed */
.sidebar.collapsed .nav-item:hover .nav-icon:not(.active-icon) {
  background: rgba(255, 255, 255, 0.9);
  color: #5521be;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.2);
}

/* Keep active icon unchanged on hover */
.sidebar.collapsed .nav-item:hover .nav-icon.active-icon {
  transform: scale(1.1);
}

/* Nav Name */
.nav-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Enhanced hover effect for collapsed sidebar */
.sidebar.collapsed .nav-item {
  position: relative;
}

.sidebar.collapsed .nav-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
}

/* User Section */
.user-section {
  padding: 1rem 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.user-email {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.user-actions {
  display: flex;
  justify-content: center;
}

.sidebar.collapsed .user-actions {
  justify-content: center;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.logout-button:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.logout-button i {
  font-size: 0.875rem;
}

.logout-text {
  font-weight: 500;
}

.sidebar.collapsed .logout-button {
  padding: 0.5rem;
  width: auto;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px !important;
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
  }

  .sidebar.mobile-open {
    transform: translateX(0) !important;
  }

  .sidebar.collapsed {
    transform: translateX(-100%) !important;
    width: 280px !important;
  }

  .sidebar.collapsed.mobile-open {
    transform: translateX(0) !important;
    width: 280px !important;
  }

  /* Hide desktop toggle button on mobile */
  .sidebar .toggle-btn {
    display: none !important;
  }
}
