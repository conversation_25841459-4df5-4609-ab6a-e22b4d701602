import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { filter, Subject, takeUntil } from 'rxjs';
import { AuthUser, ConfigService, SupabaseService } from '../../../core';
import { PortalFeatureDefinition } from '../../../core/types';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  isCollapsed = true;
  isMobileOpen = false;
  activeNav: string | null = null;
  currentUser: AuthUser | null = null;
  // Navigation items (former automations as nav links)
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
  ) {}

  // Navigation items from config
  get navItems(): PortalFeatureDefinition[] {
    return this.configService.regularNavigation;
  }

  get settingsItems(): PortalFeatureDefinition[] {
    return this.configService.settingsNavigation;
  }

  get hasSettings(): boolean {
    return this.configService.hasSettings;
  }

  ngOnInit() {
    // Set active nav based on current route
    this.updateActiveNavFromRoute();

    // Listen to router events to update active nav
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.updateActiveNavFromRoute();
      });

    // Subscribe to auth state
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: any) => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar() {
    console.log('🔄 Toggle sidebar called, isMobile:', this.isMobile());
    if (this.isMobile()) {
      this.isMobileOpen = !this.isMobileOpen;
      console.log('📱 Mobile sidebar toggled:', this.isMobileOpen);
    } else {
      this.isCollapsed = !this.isCollapsed;
      console.log('🖥️ Desktop sidebar toggled:', this.isCollapsed);
    }
  }

  closeMobileSidebar() {
    console.log('❌ Close mobile sidebar called');
    this.isMobileOpen = false;
  }

  selectFeature(feature: PortalFeatureDefinition) {
    this.activeNav = feature.id;
    this.router.navigate([feature.route]);

    // Close mobile sidebar after navigation
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  async logout() {
    await this.supabaseService.signOut();
    this.router.navigate(['/auth/login']);
  }

  getUserDisplayName(): string {
    if (!this.currentUser) return 'User';
    return this.currentUser.name || this.currentUser.email || 'User';
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const name = this.currentUser.name || this.currentUser.email || 'User';
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  private updateActiveNavFromRoute() {
    const currentUrl = this.router.url;

    // Combine all navigation items (regular + settings)
    const allNavItems = [...this.navItems, ...this.settingsItems];

    // Find matching nav item based on current route
    const matchingNav = allNavItems.find((item) => {
      // Remove leading slash from route for comparison
      const routePath = item.route.startsWith('/')
        ? item.route.substring(1)
        : item.route;
      const currentPath = currentUrl.startsWith('/')
        ? currentUrl.substring(1)
        : currentUrl;

      // Check if current path starts with the nav route
      return (
        currentPath === routePath || currentPath.startsWith(routePath + '/')
      );
    });

    if (matchingNav) {
      this.activeNav = matchingNav.id;
    } else if (allNavItems.length > 0) {
      // Fallback to first nav item if no match found
      this.activeNav = allNavItems[0].id;
    }
  }
}
