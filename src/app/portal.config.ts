import type {
  FeatureConfigRegistry,
  PortalConfig,
  PortalFeatureDefinition,
} from './core/types';
import { PortalFeature } from './core/types';
import { customerConfig } from '../../customers';
import { defaultFeatureConfigs } from './core/config/default-feature-configs';

const featureDefinitions: Record<PortalFeature, PortalFeatureDefinition> = {
  [PortalFeature.AD_WINNERS]: {
    id: PortalFeature.AD_WINNERS,
    name: 'Ad Winners',
    icon: 'pi pi-trophy',
    route: '/ad-winners',
  },
  [PortalFeature.CREATIVES_UPLOADER]: {
    id: PortalFeature.CREATIVES_UPLOADER,
    name: 'Creatives Uploader',
    icon: 'pi pi-cloud-upload',
    route: '/creatives-uploader',
  },
  [PortalFeature.FB_ACCOUNTS_MANAGER]: {
    id: PortalFeature.FB_ACCOUNTS_MANAGER,
    name: 'Facebook Accounts',
    icon: 'fab fa-facebook-f',
    route: '/fb-accounts-manager',
    isSetting: true,
  },
};

const mergedFeatureConfigs: Partial<FeatureConfigRegistry> = {};
Object.values(PortalFeature).forEach((feature) => {
  mergedFeatureConfigs[feature] = {
    ...defaultFeatureConfigs[feature],
    ...(customerConfig.featureConfigs?.[feature] || {}),
  } as any;
});

// Build portal config from customer config
export const portalConfig: PortalConfig = {
  supabase: customerConfig.supabase,
  features: customerConfig.enabledFeatures.map((featureId: PortalFeature) => {
    const feature = {
      ...featureDefinitions[featureId],
      enabled: true,
    };

    // If it's a setting feature, prefix the route with /settings
    if (feature.isSetting) {
      feature.route = `/settings${feature.route}`;
    }

    return feature;
  }),
  featureConfigs: mergedFeatureConfigs,
  n8nBaseUrl: customerConfig.n8nBaseUrl,
};
