.callback-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.callback-card {
  width: 100%;
  max-width: 420px;
  padding: 3rem 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  text-align: center;
}

/* Logo */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 220px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Status Section */
.status-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.status-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.status-message {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Loading Spinner */
.loading-spinner {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

/* Status Icons */
.status-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.success-icon {
  color: #22c55e;
}

.error-icon {
  color: #ef4444;
}

/* Button */
.button-container {
  margin-top: 1.5rem;
}

.retry-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: var(--primary-color-dark);
  transform: translateY(-1px);
}

.retry-btn:active {
  transform: translateY(0);
}

/* Responsive */
@media (max-width: 480px) {
  .callback-container {
    padding: 1rem;
  }

  .callback-card {
    padding: 2rem 1.5rem;
  }

  .status-title {
    font-size: 1.25rem;
  }

  .status-message {
    font-size: 0.9rem;
  }
}
