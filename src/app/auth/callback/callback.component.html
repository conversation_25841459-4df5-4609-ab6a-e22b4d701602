<div class="callback-container">
  <div class="callback-card">
    <!-- Logo -->
    <div class="logo-section">
      <img [src]="logoUrl" alt="Chainmatic Portal" class="logo">
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="status-section">
      <div class="loading-spinner">
        <i class="pi pi-spin pi-spinner"></i>
      </div>
      <h2 class="status-title">Processing Authentication</h2>
      <p class="status-message">Please wait while we verify your credentials...</p>
    </div>

    <!-- Success State -->
    <div *ngIf="success && !loading" class="status-section success">
      <div class="status-icon success-icon">
        <i class="pi pi-check-circle"></i>
      </div>
      <h2 class="status-title">Welcome!</h2>
      <p class="status-message">Authentication successful. Redirecting you to the portal...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !loading" class="status-section error">
      <div class="status-icon error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <h2 class="status-title">Authentication Failed</h2>
      <p class="status-message">{{ error }}</p>
      
      <div class="button-container">
        <button 
          class="retry-btn"
          (click)="onRetryClick()">
          <i class="pi pi-refresh"></i>
          Try Again
        </button>
      </div>
    </div>
  </div>
</div>
