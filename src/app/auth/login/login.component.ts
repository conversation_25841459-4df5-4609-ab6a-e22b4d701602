import { Component, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// PrimeNG
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';


import { SupabaseService } from '../../core';
import { Checkbox } from 'primeng/checkbox';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,

    Checkbox,
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  loading = false;
  error: string | null = null;
  returnUrl = '/dashboard';

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false],
    });
  }

  // Getters for template
  get logoUrl(): string {
    return 'images/chainmatic-portal.png';
  }

  ngOnInit(): void {
    // Get return URL from route parameters
    this.returnUrl =
      this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    // Listen to auth state changes
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState) => {
        this.loading = authState.loading;
        this.error = authState.error;

        // If user is authenticated, redirect
        if (authState.user && !authState.loading) {
          this.router.navigate([this.returnUrl]);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async onSubmit(): Promise<void> {
    if (this.loginForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const { email, password, rememberMe } = this.loginForm.value;

      // Save remember me preference
      if (rememberMe) {
        localStorage.setItem('chainmatic-remember-me', 'true');
      } else {
        localStorage.removeItem('chainmatic-remember-me');
      }

      const result = await this.supabaseService.signInWithEmail(email, password);

      if (result.success) {
        // Login successful - redirect will happen automatically via auth state change
        console.log('✅ Login successful', rememberMe ? '(with remember me)' : '(session only)');
      } else if (result.needsMfa) {
        // MFA verification required
        console.log('🔐 MFA verification required');
        this.router.navigate(['/auth/mfa-verify'], {
          queryParams: { returnUrl: this.returnUrl }
        });
      } else {
        this.error = result.error || 'Login failed';
        this.loading = false;
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      this.error = 'An unexpected error occurred';
      this.loading = false;
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && this.loginForm.valid && !this.loading) {
      event.preventDefault();
      this.onSubmit();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }
}
