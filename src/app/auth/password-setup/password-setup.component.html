<div class="password-setup-container">
  <div class="password-setup-card">
    <!-- Logo -->
    <div class="logo-section">
      <img [src]="logoUrl" alt="Chainmatic Portal" class="logo">
    </div>

    <!-- Header -->
    <div class="header-section">
      <h2 class="title">Set Up Your Password</h2>
      <p class="subtitle" *ngIf="userEmail">Welcome {{ userEmail }}! Please create a secure password for your account.</p>
      <p class="subtitle" *ngIf="!userEmail">Please create a secure password for your account.</p>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="error-container">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <span class="error-text">{{ error }}</span>
    </div>

    <!-- Password Setup Form -->
    <form (ngSubmit)="onSubmit()" [formGroup]="passwordForm" class="password-form">
      <!-- Password Field -->
      <div class="field">
        <label for="password" class="field-label">New Password</label>
        <p-password
          formControlName="password"
          inputId="password"
          placeholder="Enter your password"
          [toggleMask]="true"
          [feedback]="true"
          styleClass="w-full">
        </p-password>
        <div *ngIf="password?.invalid && password?.touched" class="field-error">
          <span *ngIf="password?.errors?.['required']">Password is required</span>
          <span *ngIf="password?.errors?.['minlength']">Password must be at least 8 characters</span>
        </div>
      </div>

      <!-- Confirm Password Field -->
      <div class="field">
        <label for="confirmPassword" class="field-label">Confirm Password</label>
        <p-password
          formControlName="confirmPassword"
          inputId="confirmPassword"
          placeholder="Confirm your password"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="w-full">
        </p-password>
        <div *ngIf="confirmPassword?.invalid && confirmPassword?.touched" class="field-error">
          <span *ngIf="confirmPassword?.errors?.['required']">Please confirm your password</span>
        </div>
        <div *ngIf="passwordForm.errors?.['passwordMismatch'] && confirmPassword?.touched" class="field-error">
          <span>Passwords do not match</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="button-container">
        <p-button
          [disabled]="passwordForm.invalid || loading"
          [loading]="loading"
          [label]="loading ? 'Setting up...' : 'Set Password'"
          styleClass="submit-btn w-full"
          type="submit">
        </p-button>
        
        <p-button
          [disabled]="loading"
          label="Skip for now"
          severity="secondary"
          styleClass="skip-btn w-full"
          type="button"
          (click)="onSkip()">
        </p-button>
      </div>
    </form>
  </div>
</div>
