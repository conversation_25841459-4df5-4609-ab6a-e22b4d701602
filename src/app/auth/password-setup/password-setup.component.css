.password-setup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.password-setup-card {
  width: 100%;
  max-width: 420px;
  padding: 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 220px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Header */
.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.subtitle {
  font-size: 0.9rem;
  color: var(--text-color-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Error Message */
.error-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--red-50);
  border: 1px solid var(--red-200);
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.error-icon {
  color: var(--red-500);
  font-size: 1rem;
}

.error-text {
  color: var(--red-700);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Form */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
}

.field-error {
  font-size: 0.75rem;
  color: var(--red-500);
  margin-top: 0.25rem;
}

/* Buttons */
.button-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.submit-btn {
  height: 2.75rem;
}

.skip-btn {
  height: 2.5rem;
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 480px) {
  .password-setup-container {
    padding: 1rem;
  }
  
  .password-setup-card {
    padding: 1.5rem;
  }
  
  .title {
    font-size: 1.25rem;
  }
  
  .subtitle {
    font-size: 0.85rem;
  }
}
