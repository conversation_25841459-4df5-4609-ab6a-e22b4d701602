import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// PrimeNG
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { SupabaseService } from '../../core';

@Component({
  selector: 'chm-mfa-verify',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
  ],
  templateUrl: './mfa-verify.component.html',
  styleUrls: ['./mfa-verify.component.css'],
})
export class MfaVerifyComponent implements OnInit {
  mfaForm: FormGroup;
  loading = false;
  error: string | null = null;
  userEmail = '';

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.mfaForm = this.fb.group({
      verificationCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
    });
  }

  // Getters for template
  get logoUrl(): string {
    return 'images/chainmatic-portal.png';
  }

  get verificationCode() { return this.mfaForm.get('verificationCode'); }

  ngOnInit(): void {
    // Get user email from current session if available
    const currentUser = this.supabaseService.currentUser;
    if (currentUser?.email) {
      this.userEmail = currentUser.email;
    }
  }

  async onVerifyMfa(): Promise<void> {
    if (this.mfaForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const { verificationCode } = this.mfaForm.value;

      const result = await this.supabaseService.verifyMfaLogin(verificationCode);

      if (result.success) {
        console.log('✅ MFA login verification successful');
        
        // Get return URL and redirect
        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';
        this.router.navigate([returnUrl]);
      } else {
        this.error = result.error || 'Invalid verification code';
        this.loading = false;
      }
    } catch (error) {
      console.error('❌ MFA verification error:', error);
      this.error = 'An unexpected error occurred';
      this.loading = false;
    }
  }

  onBackToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.mfaForm.controls).forEach(key => {
      const control = this.mfaForm.get(key);
      control?.markAsTouched();
    });
  }
}
