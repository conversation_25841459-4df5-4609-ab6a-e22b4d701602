<div class="mfa-verify-container">
  <div class="mfa-verify-card">
    <!-- Logo -->
    <div class="logo-section">
      <img [src]="logoUrl" alt="Chainmatic Portal" class="logo">
    </div>

    <!-- Header -->
    <div class="header-section">
      <h2 class="title">Two-Factor Authentication</h2>
      <p class="subtitle" *ngIf="userEmail">Enter the verification code from your authenticator app for {{ userEmail }}</p>
      <p class="subtitle" *ngIf="!userEmail">Enter the verification code from your authenticator app</p>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="error-container">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <span class="error-text">{{ error }}</span>
    </div>

    <!-- MFA Verification Form -->
    <form (ngSubmit)="onVerifyMfa()" [formGroup]="mfaForm" class="mfa-form">
      <div class="field">
        <label for="verificationCode" class="field-label">Verification Code</label>
        <input
          type="text"
          id="verificationCode"
          formControlName="verificationCode"
          pInputText
          placeholder="000000"
          class="field-input verification-input"
          maxlength="6"
          autocomplete="one-time-code">
        <div *ngIf="verificationCode?.invalid && verificationCode?.touched" class="field-error">
          <span *ngIf="verificationCode?.errors?.['required']">Verification code is required</span>
          <span *ngIf="verificationCode?.errors?.['pattern']">Please enter a 6-digit code</span>
        </div>
      </div>

      <div class="button-container">
        <p-button
          [disabled]="mfaForm.invalid || loading"
          [loading]="loading"
          [label]="loading ? 'Verifying...' : 'Verify'"
          styleClass="verify-btn w-full"
          type="submit">
        </p-button>
        
        <p-button
          [disabled]="loading"
          label="Back to Login"
          severity="secondary"
          styleClass="back-btn w-full"
          (click)="onBackToLogin()">
        </p-button>
      </div>
    </form>

    <!-- Help Text -->
    <div class="help-section">
      <p class="help-text">
        <i class="pi pi-info-circle"></i>
        Open your authenticator app and enter the 6-digit code
      </p>
    </div>
  </div>
</div>
