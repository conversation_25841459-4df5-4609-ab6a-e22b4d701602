.mfa-setup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.mfa-setup-card {
  width: 100%;
  max-width: 480px;
  padding: 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 220px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Header */
.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.subtitle {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Error Message */
.error-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.error-icon {
  color: #ef4444;
  font-size: 1rem;
}

.error-text {
  color: #b91c1c;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Info Section */
.info-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.info-icon {
  font-size: 2rem;
  color: #5521be;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.info-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.benefits-list {
  margin: 0;
  padding-left: 1.25rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.benefits-list li {
  margin-bottom: 0.25rem;
}

/* QR Code Section */
.step-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.step-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.qr-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.qr-code {
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.qr-image {
  width: 200px;
  height: 200px;
  display: block;
}

/* Secret Section */
.secret-section {
  width: 100%;
  text-align: center;
}

.secret-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.secret-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
}

.secret-code {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #1f2937;
  word-break: break-all;
}

.copy-btn {
  padding: 0.5rem;
  background: #5521be;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background: #4c1d95;
}

/* Form */
.verification-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  text-align: center;
}

.field-input {
  width: 100%;
  height: 2.75rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: #ffffff;
  font-size: 1rem;
  color: #1f2937;
  transition: all 0.2s ease;
}

.verification-input {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-size: 1.25rem;
  letter-spacing: 0.25rem;
}

.field-input:focus {
  outline: none;
  border-color: #5521be;
  box-shadow: 0 0 0 2px rgba(85, 33, 190, 0.2);
}

.field-error {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
  text-align: center;
}

/* Buttons */
.button-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.setup-btn, .verify-btn {
  height: 2.75rem;
}

.skip-btn {
  height: 2.5rem;
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 480px) {
  .mfa-setup-container {
    padding: 1rem;
  }
  
  .mfa-setup-card {
    padding: 1.5rem;
  }
  
  .info-section {
    flex-direction: column;
    text-align: center;
  }
  
  .qr-image {
    width: 160px;
    height: 160px;
  }
}
