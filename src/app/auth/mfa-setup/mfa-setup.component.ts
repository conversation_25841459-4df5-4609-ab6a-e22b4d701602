import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// PrimeNG
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { SupabaseService } from '../../core';

@Component({
  selector: 'chm-mfa-setup',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
  ],
  templateUrl: './mfa-setup.component.html',
  styleUrls: ['./mfa-setup.component.css'],
})
export class MfaSetupComponent implements OnInit {
  mfaForm: FormGroup;
  loading = false;
  error: string | null = null;
  qrCodeUrl: string | null = null;
  secret: string | null = null;
  showQrCode = false;
  userEmail = '';

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.mfaForm = this.fb.group({
      verificationCode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
    });
  }

  // Getters for template
  get logoUrl(): string {
    return 'images/chainmatic-portal.png';
  }

  get verificationCode() { return this.mfaForm.get('verificationCode'); }

  ngOnInit(): void {
    // Get user email from current session
    const currentUser = this.supabaseService.currentUser;
    if (currentUser?.email) {
      this.userEmail = currentUser.email;
    }
  }

  async onSetupMfa(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      const result = await this.supabaseService.setupMfa();

      if (result.success && result.qrCodeUrl && result.secret) {
        this.qrCodeUrl = result.qrCodeUrl;
        this.secret = result.secret;
        this.showQrCode = true;
        console.log('✅ MFA setup initiated');
      } else {
        this.error = result.error || 'Failed to setup MFA';
      }
    } catch (error) {
      console.error('❌ MFA setup error:', error);
      this.error = 'An unexpected error occurred';
    } finally {
      this.loading = false;
    }
  }

  async onVerifyMfa(): Promise<void> {
    if (this.mfaForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      const { verificationCode } = this.mfaForm.value;

      const result = await this.supabaseService.verifyMfa(verificationCode);

      if (result.success) {
        console.log('✅ MFA verification successful');
        
        // Get return URL and redirect
        const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';
        this.router.navigate([returnUrl]);
      } else {
        this.error = result.error || 'Invalid verification code';
        this.loading = false;
      }
    } catch (error) {
      console.error('❌ MFA verification error:', error);
      this.error = 'An unexpected error occurred';
      this.loading = false;
    }
  }

  onSkipMfa(): void {
    // Allow user to skip MFA setup
    const returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/ad-winners';
    this.router.navigate([returnUrl]);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.mfaForm.controls).forEach(key => {
      const control = this.mfaForm.get(key);
      control?.markAsTouched();
    });
  }

  copySecret(): void {
    if (this.secret) {
      navigator.clipboard.writeText(this.secret).then(() => {
        console.log('Secret copied to clipboard');
      });
    }
  }
}
