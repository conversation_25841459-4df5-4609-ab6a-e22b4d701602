<div class="mfa-setup-container">
  <div class="mfa-setup-card">
    <!-- Logo -->
    <div class="logo-section">
      <img [src]="logoUrl" alt="Chainmatic Portal" class="logo">
    </div>

    <!-- Header -->
    <div class="header-section">
      <h2 class="title">Secure Your Account</h2>
      <p class="subtitle">Set up two-factor authentication for enhanced security (optional)</p>
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="error-container">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <span class="error-text">{{ error }}</span>
    </div>

    <!-- Initial Setup Step -->
    <div *ngIf="!showQrCode" class="setup-step">
      <div class="info-section">
        <div class="info-icon">
          <i class="pi pi-shield"></i>
        </div>
        <div class="info-content">
          <h3 class="info-title">Two-Factor Authentication</h3>
          <p class="info-description">
            Add an extra layer of security to your account by requiring a verification code 
            from your authenticator app when signing in.
          </p>
          <ul class="benefits-list">
            <li>Protects against unauthorized access</li>
            <li>Works with Google Authenticator, Authy, and other TOTP apps</li>
            <li>Can be disabled later if needed</li>
          </ul>
        </div>
      </div>

      <div class="button-container">
        <p-button
          [loading]="loading"
          [label]="loading ? 'Setting up...' : 'Set Up Authenticator'"
          styleClass="setup-btn w-full"
          (click)="onSetupMfa()">
        </p-button>
        
        <p-button
          [disabled]="loading"
          label="Skip for now"
          severity="secondary"
          styleClass="skip-btn w-full"
          (click)="onSkipMfa()">
        </p-button>
      </div>
    </div>

    <!-- QR Code Step -->
    <div *ngIf="showQrCode" class="qr-step">
      <div class="step-header">
        <h3 class="step-title">Scan QR Code</h3>
        <p class="step-description">
          Open your authenticator app and scan this QR code, or manually enter the secret key.
        </p>
      </div>

      <!-- QR Code -->
      <div class="qr-section">
        <div class="qr-code" *ngIf="qrCodeUrl">
          <img [src]="qrCodeUrl" alt="QR Code for MFA setup" class="qr-image">
        </div>
        
        <!-- Manual Secret -->
        <div class="secret-section" *ngIf="secret">
          <label class="secret-label">Manual entry key:</label>
          <div class="secret-container">
            <code class="secret-code">{{ secret }}</code>
            <button class="copy-btn" (click)="copySecret()" title="Copy to clipboard">
              <i class="pi pi-copy"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Verification Form -->
      <form (ngSubmit)="onVerifyMfa()" [formGroup]="mfaForm" class="verification-form">
        <div class="field">
          <label for="verificationCode" class="field-label">Enter verification code</label>
          <input
            type="text"
            id="verificationCode"
            formControlName="verificationCode"
            pInputText
            placeholder="000000"
            class="field-input verification-input"
            maxlength="6"
            autocomplete="one-time-code">
          <div *ngIf="verificationCode?.invalid && verificationCode?.touched" class="field-error">
            <span *ngIf="verificationCode?.errors?.['required']">Verification code is required</span>
            <span *ngIf="verificationCode?.errors?.['pattern']">Please enter a 6-digit code</span>
          </div>
        </div>

        <div class="button-container">
          <p-button
            [disabled]="mfaForm.invalid || loading"
            [loading]="loading"
            [label]="loading ? 'Verifying...' : 'Verify & Complete'"
            styleClass="verify-btn w-full"
            type="submit">
          </p-button>
          
          <p-button
            [disabled]="loading"
            label="Skip MFA Setup"
            severity="secondary"
            styleClass="skip-btn w-full"
            (click)="onSkipMfa()">
          </p-button>
        </div>
      </form>
    </div>
  </div>
</div>
