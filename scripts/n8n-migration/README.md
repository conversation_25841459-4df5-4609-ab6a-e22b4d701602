# N8N Workflow Migration Tool

A TypeScript CLI tool for migrating N8N workflows between different customer environments with AWS Parameter Store integration and automatic tag management.

## Features

- 🔄 **Multi-destination migration**: Migrate workflows from one source to multiple targets
- 🏷️ **Automatic tag management**: Creates missing tags and assigns them to workflows
- 🔐 **AWS integration**: Fetches N8N API tokens from AWS Parameter Store
- 🔍 **Dry run mode**: Preview migrations without making changes
- ✅ **Overwrite support**: Update existing workflows or create new ones
- 📊 **Detailed reporting**: Shows migration results with success/failure counts

## Prerequisites

- **Node.js** (v18+)
- **AWS CLI** configured with `chainmatic` profile
- **awsume** for MFA authentication (`pip install awsume`)
- **N8N API tokens** stored in AWS Parameter Store at `/customers/{customer-id}/n8n/api-key`

## Installation

```bash
cd scripts/n8n-migration
npm install
```

## Usage

### Basic Migration

```bash
# Authenticate with AWS MFA first
awsume chainmatic

# Migrate workflows with specific tags
./migrate.ts dr-pfoten chainmatic "ad-winners"
```

### Advanced Usage

```bash
# Multiple destinations
./migrate.ts dr-pfoten staging-client,production-client "ad-winners,core"

# Dry run (preview only)
./migrate.ts dr-pfoten chainmatic "ad-winners" --dry-run

# Overwrite existing workflows
./migrate.ts dr-pfoten chainmatic "ad-winners" --overwrite

# Multiple tags (OR logic)
./migrate.ts dr-pfoten chainmatic "ad-winners,facebook-ads,automation"
```

## Command Syntax

```
./migrate.ts <source> <target1,target2,...> <tag1,tag2,...> [options]
```

### Parameters

- **source**: Source customer ID (e.g., `dr-pfoten`)
- **targets**: Comma-separated list of target customer IDs (e.g., `chainmatic,staging-client`)
- **tags**: Comma-separated list of workflow tags to migrate (e.g., `"ad-winners,core"`)

### Options

- `--dry-run`: Preview what would be migrated without making changes
- `--overwrite`: Overwrite existing workflows with the same name

## How It Works

1. **Authentication**: Uses AWS credentials (via awsume) to access Parameter Store
2. **API Token Retrieval**: Fetches N8N API tokens from AWS Parameter Store
3. **Workflow Discovery**: Finds workflows in source N8N instance by tags (OR logic)
4. **Tag Management**: 
   - Fetches existing tags from destination
   - Creates missing tags automatically
   - Assigns tags to migrated workflows
5. **Migration**: 
   - Exports workflows from source (once)
   - Imports to all destinations
   - Handles workflow settings, nodes, connections, and static data
6. **Reporting**: Shows detailed success/failure statistics

## Configuration

### AWS Parameter Store

API tokens must be stored in AWS Parameter Store:
```
/customers/dr-pfoten/n8n/api-key
/customers/chainmatic/n8n/api-key
/customers/staging-client/n8n/api-key
```

### Customer Configs

Customer configurations are loaded from:
```
customers/{customer-id}/customer.config.ts
```

Each config contains:
- Customer ID and name
- N8N base URL
- Supabase configuration
- Enabled features

## Examples

### Migrate Ad Winners workflows
```bash
awsume chainmatic && ./migrate.ts dr-pfoten chainmatic "ad-winners"
```

### Migrate to multiple environments
```bash
awsume chainmatic && ./migrate.ts dr-pfoten staging,production,backup "core,automation"
```

### Preview migration (dry run)
```bash
awsume chainmatic && ./migrate.ts dr-pfoten chainmatic "ad-winners" --dry-run
```

### Update existing workflows
```bash
awsume chainmatic && ./migrate.ts dr-pfoten chainmatic "ad-winners" --overwrite
```

## Output Example

```
🚀 Starting migration from dr-pfoten to [chainmatic]
📋 Tags: ad-winners
📡 Source: https://n8n.pfotendash.de

🎯 Targets:
   chainmatic: https://n8n.chainmatic.ai

📥 Fetching workflows from source...
📡 Fetching workflows with tag: ad-winners
Found 2 workflows with tag "ad-winners"

📋 Workflows to migrate:
1. FB Videos Scraper (tags: ad-winners)
2. FB Ad Insights Scraper (tags: ad-winners)

✅ Workflows already loaded with full data from source

🎯 Importing to chainmatic...
  📥 Importing: FB Videos Scraper
  📝 Creating tag: ad-winners
  🏷️ Assigned 1 tag(s) to workflow
  ✅ Created workflow: FB Videos Scraper
  📥 Importing: FB Ad Insights Scraper
  🏷️ Assigned 1 tag(s) to workflow
  ✅ Created workflow: FB Ad Insights Scraper

📊 Migration Summary:

  chainmatic:
    ✅ Success: 2
    ❌ Failed: 0

🎉 Migration completed!
📊 Total: 2 successful, 0 failed across 1 destination(s)
```

## Troubleshooting

### AWS MFA Required
If you see MFA errors, authenticate first:
```bash
awsume chainmatic
```

### API Token Not Found
Ensure API tokens are stored in AWS Parameter Store:
```bash
aws ssm get-parameter --name "/customers/dr-pfoten/n8n/api-key" --with-decryption
```

### Workflow Import Errors
Check N8N API compatibility and ensure:
- Source and destination N8N versions are compatible
- Workflow nodes are available in destination
- API tokens have sufficient permissions

## Development

### Type Checking
```bash
npx tsc --noEmit migrate.ts
```

### Testing
```bash
# Always test with dry run first
./migrate.ts source target "tags" --dry-run
```

## Security Notes

- API tokens are securely stored in AWS Parameter Store
- MFA authentication required for AWS access
- No sensitive data is logged or stored locally
- Workflows are migrated with all security settings intact
