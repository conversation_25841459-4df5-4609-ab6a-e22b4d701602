#!/usr/bin/env ts-node

import * as path from 'path';
import axios from 'axios';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';

// Check if AWS credentials are available
async function checkAwsCredentials(): Promise<boolean> {
  return !!(
    process.env.AWS_ACCESS_KEY_ID ||
    process.env.AWS_PROFILE ||
    process.env.AWS_SESSION_TOKEN
  );
}

// Check if awsume is available and give instructions
async function checkAwsumeAndGiveInstructions(): Promise<void> {
  console.log('🔐 No AWS credentials found.');
  console.log('');
  console.log('Please authenticate first using one of these methods:');
  console.log('');
  console.log('Option 1 - awsume (recommended):');
  console.log('  awsume chainmatic');
  console.log('  ./migrate.ts dr-pfoten chainmatic "ad-winners" --dry-run');
  console.log('');
  console.log('Option 2 - aws-vault:');
  console.log(
    '  aws-vault exec chainmatic -- ./migrate.ts dr-pfoten chainmatic "ad-winners" --dry-run',
  );
  console.log('');
  console.log('Option 3 - Manual AWS CLI:');
  console.log('  export AWS_PROFILE=chainmatic');
  console.log('  aws sts get-caller-identity  # to verify MFA works');
  console.log('  ./migrate.ts dr-pfoten chainmatic "ad-winners" --dry-run');
  console.log('');
}

// CLI Interface
async function main() {
  // Check for AWS credentials first
  if (!(await checkAwsCredentials())) {
    await checkAwsumeAndGiveInstructions();
    process.exit(1);
  }

  const args = process.argv.slice(2);

  if (args.length < 3) {
    console.log(`
Usage: ./migrate.ts <source> <target1,target2,...> <tag1,tag2,...> [options]

Options:
  --dry-run     : Preview what would be migrated without making changes
  --overwrite   : Overwrite existing workflows with same name

Requirements:
  - AWS credentials (use awsume, aws-vault, or AWS_PROFILE)
  - AWS 'chainmatic' profile configured with MFA
  - API tokens stored in AWS Parameter Store at /customers/{customer-id}/n8n/api-key

Examples:
  # Single destination
  ./migrate.ts dr-pfoten staging-client "ad-winners"

  # Multiple destinations
  ./migrate.ts dr-pfoten staging-client,production-client,backup-client "ad-winners,production"

  # With options
  ./migrate.ts dr-pfoten staging-client,production-client "core" --dry-run
  ./migrate.ts source target1,target2 "backup" --overwrite

Note: Script will check for AWS credentials and provide setup instructions if needed
      Workflows are exported once from source and imported to all destinations
`);
    process.exit(1);
  }

  const [sourceCustomer, targetCustomersStr, tagsStr] = args;
  const targetCustomers = targetCustomersStr
    .split(',')
    .map((target) => target.trim());
  const tags = tagsStr.split(',').map((tag) => tag.trim());

  const options: MigrationOptions = {
    sourceCustomer,
    targetCustomers,
    tags,
    dryRun: args.includes('--dry-run'),
    overwrite: args.includes('--overwrite'),
  };

  const migrationTool = new N8NMigrationTool();
  await migrationTool.migrate(options);
}

interface CustomerConfig {
  id: string;
  name: string;
  n8nBaseUrl: string;
  apiToken?: string;
}

interface WorkflowData {
  id: string;
  name: string;
  nodes: any[];
  connections: any;
  tags: string[];
  active: boolean;
  settings?: any;
  staticData?: any;
}

interface MigrationOptions {
  sourceCustomer: string;
  targetCustomers: string[];
  tags: string[];
  dryRun?: boolean;
  overwrite?: boolean;
}

class N8NMigrationTool {
  private customersDir = '../../customers';
  private ssmClient: SSMClient;

  constructor() {
    // Don't set AWS_PROFILE - rely on environment variables or awsume
    if (!process.env.AWS_REGION) {
      process.env.AWS_REGION = 'eu-west-2';
    }

    this.ssmClient = new SSMClient({
      region: process.env.AWS_REGION || 'eu-west-2',
    });
  }

  // Fetch all existing tags from N8N instance
  async fetchTags(
    config: CustomerConfig,
  ): Promise<{ id: string; name: string }[]> {
    const url = `${config.n8nBaseUrl}/api/v1/tags`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      const response = await axios.get(url, { headers });
      return response.data.data || response.data || [];
    } catch (error) {
      console.warn(
        `⚠️ Failed to fetch tags from ${config.n8nBaseUrl}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return [];
    }
  }

  // Create a new tag
  async createTag(
    config: CustomerConfig,
    tagName: string,
  ): Promise<{ id: string; name: string } | null> {
    const url = `${config.n8nBaseUrl}/api/v1/tags`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      const response = await axios.post(url, { name: tagName }, { headers });
      return response.data;
    } catch (error) {
      console.warn(
        `⚠️ Failed to create tag "${tagName}" in ${config.n8nBaseUrl}: ${error instanceof Error ? error.message : String(error)}`,
      );
      return null;
    }
  }

  // Assign tags to a workflow
  async assignTagsToWorkflow(
    config: CustomerConfig,
    workflowId: string,
    tagIds: string[],
  ): Promise<void> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows/${workflowId}/tags`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      // N8N API expects array of objects with id property
      const tagObjects = tagIds.map(id => ({ id }));
      await axios.put(url, tagObjects, { headers });
    } catch (error) {
      let errorMessage = `Failed to assign tags to workflow ${workflowId}`;

      if (error instanceof Error && (error as any).response) {
        const axiosError = error as any;
        errorMessage += `: HTTP ${axiosError.response.status}`;

        if (axiosError.response.data) {
          console.error(`\n🔍 Tag assignment error details:`);
          console.error('URL:', url);
          console.error('Payload:', JSON.stringify(tagIds.map(id => ({ id })), null, 2));
          console.error('Status:', axiosError.response.status);
          console.error(
            'Response:',
            JSON.stringify(axiosError.response.data, null, 2),
          );
        }
      } else {
        errorMessage += `: ${error instanceof Error ? error.message : String(error)}`;
      }

      console.warn(`⚠️ ${errorMessage}`);
    }
  }

  // Handle workflow tags - fetch existing, create missing, and assign
  async handleWorkflowTags(
    config: CustomerConfig,
    workflowId: string,
    tagNames: string[],
  ): Promise<void> {
    try {
      // Fetch all existing tags
      const existingTags = await this.fetchTags(config);
      const existingTagMap = new Map(
        existingTags.map((tag) => [tag.name, tag.id]),
      );

      const tagIds: string[] = [];

      // Process each tag name
      for (const tagName of tagNames) {
        if (existingTagMap.has(tagName)) {
          // Tag exists, use its ID
          tagIds.push(existingTagMap.get(tagName)!);
        } else {
          // Tag doesn't exist, create it
          console.log(`📝 Creating tag: ${tagName}`);
          const newTag = await this.createTag(config, tagName);
          if (newTag) {
            tagIds.push(newTag.id);
          }
        }
      }

      // Assign all tags to the workflow
      if (tagIds.length > 0) {
        await this.assignTagsToWorkflow(config, workflowId, tagIds);
        console.log(`🏷️ Assigned ${tagIds.length} tag(s) to workflow`);
      }
    } catch (error) {
      console.warn(
        `⚠️ Failed to handle tags for workflow ${workflowId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async getApiTokenFromParameterStore(
    customerId: string,
  ): Promise<string | null> {
    const parameterName = `/customers/${customerId}/n8n/api-key`;

    try {
      const command = new GetParameterCommand({
        Name: parameterName,
        WithDecryption: true, // For SecureString parameters
      });

      const response = await this.ssmClient.send(command);
      return response.Parameter?.Value || null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('multi-factor authentication')) {
        console.error(`\n❌ AWS MFA authentication required for ${customerId}`);
        console.error('Please run: awsume chainmatic');
        process.exit(1);
      } else {
        console.warn(
          `⚠️  Could not fetch API token from Parameter Store for ${customerId}: ${errorMessage}`,
        );
      }
      return null;
    }
  }

  async loadCustomerConfig(customerId: string): Promise<CustomerConfig> {
    try {
      // Import the customer config directly using dynamic import
      const configPath = path.resolve(
        this.customersDir,
        customerId,
        'customer.config.ts',
      );
      const { customerConfig } = await import(configPath);

      // Get API token from Parameter Store (with MFA prompt if needed)
      const apiToken = await this.getApiTokenFromParameterStore(customerId);

      if (!apiToken) {
        throw new Error(`No API token found for ${customerId}`);
      }

      return {
        ...customerConfig,
        apiToken,
      };
    } catch (error) {
      throw new Error(
        `Failed to load customer config for ${customerId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  async fetchWorkflowsByTag(
    config: CustomerConfig,
    tag: string,
  ): Promise<WorkflowData[]> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows?tags=${encodeURIComponent(tag)}`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      console.log(`📡 Fetching workflows with tag: ${tag}`);
      const response = await axios.get(url, { headers });
      const workflows = response.data.data || response.data;

      // Normalize tags to string arrays
      workflows.forEach((workflow: any) => {
        if (workflow.tags) {
          workflow.tags = this.extractTagNames(workflow.tags);
        }
      });

      console.log(`Found ${workflows.length} workflows with tag "${tag}"`);
      return workflows;
    } catch (error) {
      let errorMessage = `Failed to fetch workflows with tag "${tag}" from ${config.n8nBaseUrl}`;

      if (error instanceof Error && (error as any).response) {
        const axiosError = error as any;
        errorMessage += `: HTTP ${axiosError.response.status}`;

        if (axiosError.response.data) {
          console.error(`\n🔍 API Error Details:`);
          console.error('URL:', url);
          console.error('Status:', axiosError.response.status);
          console.error(
            'Response:',
            JSON.stringify(axiosError.response.data, null, 2),
          );
        }
      } else {
        errorMessage += `: ${error instanceof Error ? error.message : String(error)}`;
      }

      throw new Error(errorMessage);
    }
  }

  async fetchWorkflows(config: CustomerConfig): Promise<WorkflowData[]> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      const response = await axios.get(url, { headers });
      return response.data.data || response.data;
    } catch (error) {
      throw new Error(
        `Failed to fetch workflows from ${config.n8nBaseUrl}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  // since fetchWorkflowsByTag already returns full workflow data
  async exportWorkflow(
    config: CustomerConfig,
    workflowId: string,
  ): Promise<WorkflowData> {
    const url = `${config.n8nBaseUrl}/api/v1/workflows/${workflowId}`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    try {
      const response = await axios.get(url, { headers });
      return response.data;
    } catch (error) {
      throw new Error(
        `Failed to export workflow ${workflowId}: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  // Note: This method is kept for potential future use, but not needed for migration

  async importWorkflow(
    config: CustomerConfig,
    workflow: WorkflowData,
    overwrite = false,
  ): Promise<void> {
    const baseUrl = `${config.n8nBaseUrl}/api/v1/workflows`;
    const headers: any = {
      'Content-Type': 'application/json',
    };

    if (config.apiToken) {
      headers['X-N8N-API-KEY'] = config.apiToken;
    }

    // Extract only the fields that N8N API expects for workflow creation
    const workflowData: any = {
      name: workflow.name,
      nodes: workflow.nodes,
      connections: workflow.connections,
      settings: workflow.settings || {}, // Required field
      staticData: workflow.staticData || null, // Can be string, object, or null
    };

    // Tags are not mentioned in the required schema, so we'll skip them for now
    // to avoid the "additional properties" error

    try {
      let workflowId: string;

      if (overwrite) {
        // Try to find existing workflow by name and update
        const existing = await this.findWorkflowByName(config, workflow.name);
        if (existing) {
          await axios.put(`${baseUrl}/${existing.id}`, workflowData, {
            headers,
          });
          workflowId = existing.id;
          console.log(`✅ Updated workflow: ${workflow.name}`);
        } else {
          // Create new workflow if not found
          const response = await axios.post(baseUrl, workflowData, { headers });
          workflowId = response.data.id;
          console.log(`✅ Created workflow: ${workflow.name}`);
        }
      } else {
        // Create new workflow
        const response = await axios.post(baseUrl, workflowData, { headers });
        workflowId = response.data.id;
        console.log(`✅ Created workflow: ${workflow.name}`);
      }

      // Handle tags separately if the workflow has any
      if (workflow.tags && workflow.tags.length > 0) {
        await this.handleWorkflowTags(config, workflowId, workflow.tags);
      }
    } catch (error) {
      let errorMessage = `Failed to import workflow ${workflow.name}`;

      if (error instanceof Error && (error as any).response) {
        const axiosError = error as any;
        errorMessage += `: HTTP ${axiosError.response.status}`;

        if (axiosError.response.data) {
          console.error(`\n🔍 Detailed error for ${workflow.name}:`);
          console.error('Status:', axiosError.response.status);
          console.error(
            'Response:',
            JSON.stringify(axiosError.response.data, null, 2),
          );
        }
      } else {
        errorMessage += `: ${error instanceof Error ? error.message : String(error)}`;
      }

      throw new Error(errorMessage);
    }
  }

  async findWorkflowByName(
    config: CustomerConfig,
    name: string,
  ): Promise<WorkflowData | null> {
    const workflows = await this.fetchWorkflows(config);
    return workflows.find((w) => w.name === name) || null;
  }

  filterWorkflowsByTags(
    workflows: WorkflowData[],
    tags: string[],
  ): WorkflowData[] {
    // This method is now deprecated since we fetch by tag directly
    // Kept for backward compatibility if needed
    return workflows.filter((workflow) => {
      if (!workflow.tags || workflow.tags.length === 0) return false;
      return tags.some((tag) => workflow.tags.includes(tag));
    });
  }

  async fetchAllWorkflowsByTags(
    config: CustomerConfig,
    tags: string[],
  ): Promise<WorkflowData[]> {
    const allWorkflows = new Map<string, WorkflowData>();

    // Fetch workflows for each tag separately
    for (const tag of tags) {
      try {
        const workflows = await this.fetchWorkflowsByTag(config, tag);

        // Add to map to avoid duplicates (workflow can have multiple tags)
        workflows.forEach((workflow) => {
          allWorkflows.set(workflow.id, workflow);
        });
      } catch (error) {
        console.error(
          `❌ Failed to fetch workflows for tag "${tag}": ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    return Array.from(allWorkflows.values());
  }

  async migrate(options: MigrationOptions): Promise<void> {
    console.log(
      `🚀 Starting migration from ${options.sourceCustomer} to [${options.targetCustomers.join(', ')}]`,
    );
    console.log(`📋 Tags: ${options.tags.join(', ')}`);

    if (options.dryRun) {
      console.log('🔍 DRY RUN MODE - No changes will be made');
    }

    try {
      // Load source config
      const sourceConfig = await this.loadCustomerConfig(
        options.sourceCustomer,
      );
      console.log(`📡 Source: ${sourceConfig.n8nBaseUrl}`);

      // Load all target configs
      const targetConfigs = new Map<string, CustomerConfig>();
      console.log('\n🎯 Targets:');

      for (const targetCustomer of options.targetCustomers) {
        try {
          const targetConfig = await this.loadCustomerConfig(targetCustomer);
          targetConfigs.set(targetCustomer, targetConfig);
          console.log(`   ${targetCustomer}: ${targetConfig.n8nBaseUrl}`);
        } catch (error) {
          console.error(
            `❌ Failed to load config for ${targetCustomer}: ${error instanceof Error ? error.message : String(error)}`,
          );
          console.log('Continuing with other targets...');
        }
      }

      if (targetConfigs.size === 0) {
        console.error('❌ No valid target configurations found');
        return;
      }

      // Fetch workflows from source using individual tag requests (OR logic)
      console.log('\n📥 Fetching workflows from source...');
      const workflows = await this.fetchAllWorkflowsByTags(
        sourceConfig,
        options.tags,
      );

      if (workflows.length === 0) {
        console.log('❌ No workflows found with the specified tags');
        return;
      }

      // List workflows to be migrated
      console.log('\n📋 Workflows to migrate:');
      workflows.forEach((workflow, index) => {
        const tagNames = workflow.tags?.join(', ') || 'none';
        console.log(`${index + 1}. ${workflow.name} (tags: ${tagNames})`);
      });

      console.log(
        `\n📊 Will migrate ${workflows.length} workflow(s) to ${targetConfigs.size} destination(s)`,
      );

      if (options.dryRun) {
        console.log('\n✅ Dry run completed - no changes made');
        return;
      }

      // Workflows are already fully loaded from the tag API, no need to export them again
      console.log('\n✅ Workflows already loaded with full data from source');
      const exportedWorkflows = workflows;

      // Import to each destination
      console.log('\n📥 Importing to destinations...');
      const results = new Map<
        string,
        { success: number; failed: number; errors: string[] }
      >();

      for (const [targetCustomer, targetConfig] of Array.from(
        targetConfigs.entries(),
      )) {
        console.log(`\n🎯 Importing to ${targetCustomer}...`);

        const result = { success: 0, failed: 0, errors: [] as string[] };

        for (const workflow of exportedWorkflows) {
          try {
            console.log(`  📥 Importing: ${workflow.name}`);
            await this.importWorkflow(
              targetConfig,
              workflow,
              options.overwrite,
            );
            result.success++;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error);
            console.error(
              `  ❌ Failed to import ${workflow.name}: ${errorMessage}`,
            );
            result.failed++;
            result.errors.push(`${workflow.name}: ${errorMessage}`);
          }
        }

        results.set(targetCustomer, result);
      }

      // Summary report
      console.log('\n📊 Migration Summary:');
      for (const [targetCustomer, result] of Array.from(results.entries())) {
        console.log(`\n  ${targetCustomer}:`);
        console.log(`    ✅ Success: ${result.success}`);
        console.log(`    ❌ Failed: ${result.failed}`);

        if (result.errors.length > 0) {
          console.log(`    Errors:`);
          result.errors.forEach((error) => console.log(`      - ${error}`));
        }
      }

      const totalSuccess = Array.from(results.values()).reduce(
        (sum, r) => sum + r.success,
        0,
      );
      const totalFailed = Array.from(results.values()).reduce(
        (sum, r) => sum + r.failed,
        0,
      );

      console.log(`\n🎉 Migration completed!`);
      console.log(
        `📊 Total: ${totalSuccess} successful, ${totalFailed} failed across ${targetConfigs.size} destination(s)`,
      );
    } catch (error) {
      console.error(
        `❌ Migration failed: ${error instanceof Error ? error.message : String(error)}`,
      );
      process.exit(1);
    }
  }

  // Helper function to extract tag names from N8N tag objects
  private extractTagNames(
    tags: (string | { name?: string; id?: string })[],
  ): string[] {
    return tags.map((tag) => {
      if (typeof tag === 'string') {
        return tag;
      }
      return tag.name || tag.id || String(tag);
    });
  }
}

export { N8NMigrationTool, MigrationOptions };

// Execute main function if this file is run directly
if (require.main === module) {
  main().catch(console.error);
}
